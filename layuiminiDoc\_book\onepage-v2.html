
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>单页版 · layuimini开发手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="zhongshaofa">
        
        
    
    <link rel="stylesheet" href="gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-anchor-nav-x/style/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-tbfed-pagefooter/footer.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-image-captions/image-captions.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-code/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-insert-logo/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-donate/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="onepage-v2.html" />
    
    
    <link rel="prev" href="iframe-v2.html" />
    

    <style>
    @media only screen and (max-width: 640px) {
        .book-header .hidden-mobile {
            display: none;
        }
    }
    </style>
    <script>
        window["gitbook-plugin-github-buttons"] = {"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]};
    </script>

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="./">
            
                <a href="./">
            
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" >
            
                <span>
            
                    
                    初始化接口后端示例(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="init/sql.html">
            
                <a href="init/sql.html">
            
                    
                    数据库结构示例
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="init/laravel.html">
            
                <a href="init/laravel.html">
            
                    
                    PHP示例(Laravel)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="init/thinkphp.html">
            
                <a href="init/thinkphp.html">
            
                    
                    PHP示例(ThinkPHP)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.4" data-path="init/golang.html">
            
                <a href="init/golang.html">
            
                    
                    Golang示例(beego)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.5" data-path="init/java.html">
            
                <a href="init/java.html">
            
                    
                    Java示例(spring)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.6" data-path="init/netcore.html">
            
                <a href="init/netcore.html">
            
                    
                    ASP.NET CORE WebApi示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" >
            
                <span>
            
                    
                    使用说明(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.2" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.3" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.4" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.5" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.6" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（标签）">
            
                    
                    在页面中弹出新的Tab窗口（标签）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.7" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（JS方法）">
            
                    
                    在页面中弹出新的Tab窗口（JS方法）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.8" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.9" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="********" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter active" data-level="1.3.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html">
            
                    
                    单页版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="*******" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.3" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.4" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.5" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.6" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在页面中打开新的页面">
            
                    
                    在页面中打开新的页面
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.7" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在内容页面中返回主页">
            
                    
                    在内容页面中返回主页
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.8" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.9" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" >
            
                <span>
            
                    
                    使用说明(V1版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="iframe.html">
            
                <a href="iframe.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1.1" data-path="iframe.html">
            
                <a href="iframe.html#默认配置说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.2" data-path="iframe.html">
            
                <a href="iframe.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.3" data-path="iframe.html">
            
                <a href="iframe.html#初始化api地址返回的参数说明">
            
                    
                    初始化api地址返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.4" data-path="iframe.html">
            
                <a href="iframe.html#在页面中弹出新的Tab窗口">
            
                    
                    在页面中弹出新的Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.5" data-path="iframe.html">
            
                <a href="iframe.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.6" data-path="iframe.html">
            
                <a href="iframe.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.7" data-path="iframe.html">
            
                <a href="iframe.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="onepage.html">
            
                <a href="onepage.html">
            
                    
                    单页版
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="." >单页版</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-minus-circle"></i><ul><li><span class="title-icon "></span><a href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v2&#x7248;&#x672C;&#xFF09;"><b></b>&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762; v2&#x7248;&#x672C;&#xFF09;</a></li><li><span class="title-icon "></span><a href="#&#x66F4;&#x65B0;&#x8BF4;&#x660E;"><b></b>&#x66F4;&#x65B0;&#x8BF4;&#x660E;</a></li><li><span class="title-icon "></span><a href="#&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;"><b></b>&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;</a></li><li><span class="title-icon "></span><a href="#&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><b></b>&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;</a></li><li><span class="title-icon "></span><a href="#&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><b></b>&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</a></li><li><span class="title-icon "></span><a href="#&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><b></b>&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</a></li><li><span class="title-icon "></span><a href="#&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;"><b></b>&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;</a></li><li><span class="title-icon "></span><a href="#&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;"><b></b>&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;</a></li><li><span class="title-icon "></span><a href="#&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><b></b>&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;</a></li><li><span class="title-icon "></span><a href="#&#x5E38;&#x89C1;&#x95EE;&#x9898;"><b></b>&#x5E38;&#x89C1;&#x95EE;&#x9898;</a></li><li><span class="title-icon "></span><a href="#&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><b></b>&#x5907;&#x6CE8;&#x4FE1;&#x606F;</a></li></ul></div><a href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v2&#x7248;&#x672C;&#xFF09;" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><h1 id="&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v2&#x7248;&#x672C;&#xFF09;"><a name="&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v2&#x7248;&#x672C;&#xFF09;" class="anchor-navigation-ex-anchor" href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v2&#x7248;&#x672C;&#xFF09;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762; v2&#x7248;&#x672C;&#xFF09;</h1>
<h1 id="&#x66F4;&#x65B0;&#x8BF4;&#x660E;"><a name="&#x66F4;&#x65B0;&#x8BF4;&#x660E;" class="anchor-navigation-ex-anchor" href="#&#x66F4;&#x65B0;&#x8BF4;&#x660E;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x66F4;&#x65B0;&#x8BF4;&#x660E;</h1>
<blockquote>
<p>V2&#x7248;&#x672C;&#x76F8;&#x6BD4;&#x4E8E;V1&#xFF0C;&#x6838;&#x5FC3;&#x4EE3;&#x7801;&#x8FDB;&#x884C;&#x91CD;&#x6784;&#xFF0C;&#x66F4;&#x52A0;&#x66F4;&#x52A0;&#x89C4;&#x8303;&#xFF0C;&#x914D;&#x7F6E;&#x4F7F;&#x7528;&#x8D77;&#x6765;&#x4E5F;&#x66F4;&#x65B9;&#x4FBF;&#x3002;</p>
</blockquote>
<ul>
<li>js&#x6A21;&#x5757;&#x7684;&#x62C6;&#x5206;&#xFF0C;&#x4EE3;&#x7801;&#x66F4;&#x52A0;&#x89C4;&#x8303;&#x5316;&#x3002;</li>
<li>&#x914D;&#x7F6E;&#x9879;&#x79FB;&#x51FA;&#x5230;&#x5916;&#x90E8;&#x7684;&#x521D;&#x59CB;&#x5316;&#x914D;&#x7F6E;&#x91CC;&#x9762;&#x3002;</li>
<li>&#x5355;&#x9875;&#x5C01;&#x88C5;&#x8FDB;&#x884C;&#x91CD;&#x6784;&#xFF0C;&#x89C6;&#x89C9;&#x548C;&#x64CD;&#x4F5C;&#x4F53;&#x9A8C;&#x4E0A;&#x66F4;&#x52A0;&#x826F;&#x597D;&#x3002;</li>
<li>&#x65B0;&#x589E;&#x83DC;&#x5355;&#x5728;&#x521D;&#x59CB;&#x5316;&#x7684;&#x65F6;&#x5019;&#x53EF;&#x4EE5;&#x5C55;&#x5F00;&#x5B50;&#x83DC;&#x5355;&#x3002;</li>
<li>&#x65B0;&#x589E;&#x521D;&#x59CB;&#x5316;&#x65F6;&#x4E00;&#x4E2A;&#x914D;&#x7F6E;&#x9879;&#x5B8C;&#x6210;<code>&#x591A;&#x6A21;&#x5757;</code>&#x548C;<code>&#x5355;&#x6A21;&#x5757;</code>&#x4E4B;&#x95F4;&#x7684;&#x5207;&#x6362;&#xFF0C;&#x63A5;&#x53E3;&#x7684;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#x8FD8;&#x662F;&#x4E00;&#x6837;&#x7684;&#x3002;</li>
<li>&#x4F18;&#x5316;&#x624B;&#x673A;&#x7AEF;&#x521D;&#x59CB;&#x5316;&#x65F6;&#x7684;&#x81EA;&#x9002;&#x5E94;&#xFF0C;&#x4E0D;&#x4F1A;&#x51FA;&#x73B0;&#x95EA;&#x52A8;&#x7684;&#x95EE;&#x9898;&#x3002;</li>
<li>&#x91CD;&#x6784;&#x624B;&#x673A;&#x7AEF;&#x5DE6;&#x4FA7;&#x83DC;&#x5355;&#xFF0C;&#x5F39;&#x51FA;&#x83DC;&#x5355;&#x65F6;&#x4E0D;&#x4F1A;&#x6324;&#x538B;&#x5185;&#x5BB9;&#x5185;&#x9762;&#x3002;</li>
<li>&#x4F18;&#x5316;&#x521D;&#x59CB;&#x5316;&#x65F6;&#x7684;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x6570;&#x636E;&#x683C;&#x5F0F;<code>api/init.json</code>&#xFF0C;&#x4EE5;&#x9002;&#x914D;&#x5355;&#x6A21;&#x5757;&#x7684;&#x5207;&#x6362;&#x3002;</li>
<li>&#x65B0;&#x589E;&#x521D;&#x59CB;&#x5316;&#x52A0;&#x8F7D;&#x5C42;&#xFF0C;&#x66F4;&#x597D;&#x7684;&#x89C6;&#x89C9;&#x4F53;&#x9A8C;</li>
<li>&#x65B0;&#x589E;&#x521D;&#x59CB;&#x5316;&#x9875;&#x9762;&#x662F;&#x5426;&#x52A0;&#x7248;&#x672C;&#x53F7;</li>
<li>&#x4F18;&#x5316;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;&#x6309;&#x94AE;&#x4EE5;&#x53CA;&#x9875;&#x9762;&#x5BFC;&#x822A;&#x680F;&#x7684;&#x5B9E;&#x73B0;</li>
<li>&#x4F18;&#x5316;&#x4E3B;&#x9898;&#x914D;&#x8272;&#x65B9;&#x6848;</li>
</ul>
<h1 id="&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;"><a name="&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;" class="anchor-navigation-ex-anchor" href="#&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x57FA;&#x7840;&#x53C2;&#x6570;&#x4E00;&#x89C8;&#x8868;</h1>
<blockquote>
<p>&#x4EE5;&#x4E0B;&#x53C2;&#x6570;&#x662F;<code>miniAdmin.render();</code>&#x521D;&#x59CB;&#x5316;&#x65F6;&#x8FDB;&#x884C;&#x4F20;&#x5165;&#x3002;</p>
</blockquote>
<table>
<thead>
<tr>
<th>&#x53C2;&#x6570;</th>
<th>&#x8BF4;&#x660E;</th>
<th>&#x7C7B;&#x578B;</th>
<th>&#x9ED8;&#x8BA4;&#x503C;</th>
<th>&#x5907;&#x6CE8;</th>
</tr>
</thead>
<tbody>
<tr>
<td>iniUrl</td>
<td>&#x521D;&#x59CB;&#x5316;&#x63A5;&#x53E3;</td>
<td>string</td>
<td>null</td>
<td>&#x5B9E;&#x9645;&#x4F7F;&#x7528;&#xFF0C;&#x8BF7;&#x5BF9;&#x63A5;&#x540E;&#x7AEF;&#x63A5;&#x53E3;&#x52A8;&#x6001;&#x751F;&#x6210;&#xFF0C;&#x683C;&#x5F0F;&#x8BF7;&#x53C2;&#x8003;&#x6587;&#x4EF6;&#xFF1A;<code>api/init.json</code></td>
</tr>
<tr>
<td>clearUrl</td>
<td>&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;</td>
<td>string</td>
<td>null</td>
<td>&#x5B9E;&#x9645;&#x4F7F;&#x7528;&#xFF0C;&#x8BF7;&#x5BF9;&#x63A5;&#x540E;&#x7AEF;&#x63A5;&#x53E3;&#x52A8;&#x6001;&#x751F;&#x6210;&#xFF0C;&#x683C;&#x5F0F;&#x8BF7;&#x53C2;&#x8003;&#x6587;&#x4EF6;&#xFF1A;<code>api/init.json</code></td>
</tr>
<tr>
<td>renderPageVersion</td>
<td>&#x521D;&#x59CB;&#x5316;&#x9875;&#x9762;&#x662F;&#x5426;&#x52A0;&#x7248;&#x672C;&#x53F7;</td>
<td>bool</td>
<td>false</td>
<td>&#x5F00;&#x542F;&#x540E;&#xFF0C;&#x9875;&#x9762;&#x4E0D;&#x4F1A;&#x6709;&#x7F13;&#x5B58;&#x95EE;&#x9898;</td>
</tr>
<tr>
<td>bgColorDefault</td>
<td>&#x4E3B;&#x9898;&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;</td>
<td>int</td>
<td>0</td>
<td>&#x5982;&#x9700;&#x6DFB;&#x52A0;&#x66F4;&#x591A;&#x4E3B;&#x9898;&#x4FE1;&#x606F;&#xFF0C;&#x8BF7;&#x5728;<code>js/lay-module/layuimini/miniTheme.js</code>&#x6587;&#x4EF6;&#x5185;&#x6DFB;&#x52A0;</td>
</tr>
<tr>
<td>multiModule</td>
<td>&#x662F;&#x5426;&#x5F00;&#x542F;&#x591A;&#x6A21;&#x5757;</td>
<td>bool</td>
<td>false</td>
<td>&#x4E2A;&#x4EBA;&#x5EFA;&#x8BAE;&#x5F00;&#x542F;</td>
</tr>
<tr>
<td>menuChildOpen</td>
<td>&#x662F;&#x5426;&#x9ED8;&#x8BA4;&#x5C55;&#x5F00;&#x83DC;&#x5355;</td>
<td>bool</td>
<td>false</td>
<td>&#x4E2A;&#x4EBA;&#x5EFA;&#x8BAE;&#x5173;&#x95ED;</td>
</tr>
<tr>
<td>loadingTime</td>
<td>&#x521D;&#x59CB;&#x5316;&#x52A0;&#x8F7D;&#x65F6;&#x95F4;</td>
<td>0</td>
<td>0</td>
<td>&#x5EFA;&#x8BAE;0-2&#x4E4B;&#x95F4;</td>
</tr>
</tbody>
</table>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">        <span class="hljs-keyword">var</span> options = {
            <span class="hljs-attr">iniUrl</span>: <span class="hljs-string">&quot;api/init.json&quot;</span>,    <span class="hljs-comment">// &#x521D;&#x59CB;&#x5316;&#x63A5;&#x53E3;</span>
            clearUrl: <span class="hljs-string">&quot;api/clear.json&quot;</span>, <span class="hljs-comment">// &#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;</span>
            urlHashLocation: <span class="hljs-literal">true</span>,      <span class="hljs-comment">// &#x662F;&#x5426;&#x6253;&#x5F00;hash&#x5B9A;&#x4F4D;</span>
            bgColorDefault: <span class="hljs-number">0</span>,          <span class="hljs-comment">// &#x4E3B;&#x9898;&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;</span>
            multiModule: <span class="hljs-literal">true</span>,          <span class="hljs-comment">// &#x662F;&#x5426;&#x5F00;&#x542F;&#x591A;&#x6A21;&#x5757;</span>
            menuChildOpen: <span class="hljs-literal">false</span>,       <span class="hljs-comment">// &#x662F;&#x5426;&#x9ED8;&#x8BA4;&#x5C55;&#x5F00;&#x83DC;&#x5355;</span>
            loadingTime: <span class="hljs-number">0</span>,             <span class="hljs-comment">// &#x521D;&#x59CB;&#x5316;&#x52A0;&#x8F7D;&#x65F6;&#x95F4;</span>
        };
        miniAdmin.render(options);
</code></pre>
<h1 id="&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><a name="&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;" class="anchor-navigation-ex-anchor" href="#&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;</h1>
<ul>
<li><p>&#x5728;<code>index.html</code>&#x6587;&#x4EF6;&#x5185;&#x8FDB;&#x884C;&#x521D;&#x59CB;&#x5316;</p>
</li>
<li><p>&#x5F15;&#x5165;<code>lay-config.js</code>&#x6587;&#x4EF6;&#xFF0C;&#x8BF7;&#x6839;&#x636E;&#x5B9E;&#x9645;&#x60C5;&#x51B5;&#x4FEE;&#x6539;&#x91CC;&#x9762;&#x6269;&#x5C55;&#x7684;&#x8DEF;&#x5F84;&#x3002;</p>
</li>
<li><p>&#x5F15;&#x5165;miniAdmin&#x6A21;&#x5757;&#xFF0C;&#x6839;&#x636E;&#x9700;&#x8981;&#x4F20;&#x5165;&#x521D;&#x59CB;&#x5316;&#x53C2;&#x6570;&#xFF0C;&#x6267;&#x884C;<code>miniAdmin.render(options);</code> &#x65B9;&#x6CD5;&#x3002;</p>
</li>
<li><p>&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x53EF;&#x4EE5;&#x53C2;&#x8003;<code>api&#x76EE;&#x5F55;&#x4E0B;&#x7684;init.json&#x6587;&#x4EF6;</code>&#x6216;&#x8005;&#x67E5;&#x770B;&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#x7684;&#x7B2C;&#x4E8C;&#x70B9;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</p>
</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    layui.use([<span class="hljs-string">&apos;jquery&apos;</span>, <span class="hljs-string">&apos;layer&apos;</span>, <span class="hljs-string">&apos;miniAdmin&apos;</span>], <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
        <span class="hljs-keyword">var</span> $ = layui.jquery,
            layer = layui.layer,
            miniAdmin = layui.miniAdmin;

        <span class="hljs-keyword">var</span> options = {
            <span class="hljs-attr">iniUrl</span>: <span class="hljs-string">&quot;api/init.json&quot;</span>,    <span class="hljs-comment">// &#x521D;&#x59CB;&#x5316;&#x63A5;&#x53E3;</span>
            clearUrl: <span class="hljs-string">&quot;api/clear.json&quot;</span>, <span class="hljs-comment">// &#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;</span>
            urlHashLocation: <span class="hljs-literal">true</span>,      <span class="hljs-comment">// &#x662F;&#x5426;&#x6253;&#x5F00;hash&#x5B9A;&#x4F4D;</span>
            bgColorDefault: <span class="hljs-number">0</span>,          <span class="hljs-comment">// &#x4E3B;&#x9898;&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;</span>
            multiModule: <span class="hljs-literal">true</span>,          <span class="hljs-comment">// &#x662F;&#x5426;&#x5F00;&#x542F;&#x591A;&#x6A21;&#x5757;</span>
            menuChildOpen: <span class="hljs-literal">false</span>,       <span class="hljs-comment">// &#x662F;&#x5426;&#x9ED8;&#x8BA4;&#x5C55;&#x5F00;&#x83DC;&#x5355;</span>
        };
        miniAdmin.render(options);

    });
</code></pre>
<h1 id="&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><a name="&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;" class="anchor-navigation-ex-anchor" href="#&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x521D;&#x59CB;&#x5316;api&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</h1>
<ul>
<li><p><code>homeInfo</code> &#x662F;&#x9996;&#x9875;&#x4FE1;&#x606F;</p>
</li>
<li><p><code>logoInfo</code> &#x662F;logo&#x4FE1;&#x606F;</p>
</li>
<li><p><code>menuInfo</code> &#x662F;&#x5934;&#x90E8;&#x6A21;&#x5757;&#x548C;&#x5DE6;&#x4FA7;&#x83DC;&#x5355;&#x5BF9;&#x5E94;&#x7684;&#x4FE1;&#x606F;</p>
</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-json">{
  <span class="hljs-string">&quot;homeInfo&quot;</span>: {
    <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x9996;&#x9875;&quot;</span>,
    <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;page/welcome-1.html?t=1&quot;</span>
  },
  <span class="hljs-string">&quot;logoInfo&quot;</span>: {
    <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;LAYUI MINI&quot;</span>,
    <span class="hljs-string">&quot;image&quot;</span>: <span class="hljs-string">&quot;images/logo.png&quot;</span>,
    <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;&quot;</span>
  },
  <span class="hljs-string">&quot;menuInfo&quot;</span>: [
    {
      <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x5E38;&#x89C4;&#x7BA1;&#x7406;&quot;</span>,
      <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-address-book&quot;</span>,
      <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;&quot;</span>,
      <span class="hljs-string">&quot;target&quot;</span>: <span class="hljs-string">&quot;_self&quot;</span>,
      <span class="hljs-string">&quot;child&quot;</span>:[...]
    },
    {
      <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x7EC4;&#x4EF6;&#x7BA1;&#x7406;&quot;</span>,
      <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-lemon-o&quot;</span>,
      <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;&quot;</span>,
      <span class="hljs-string">&quot;target&quot;</span>: <span class="hljs-string">&quot;_self&quot;</span>,
      <span class="hljs-string">&quot;child&quot;</span>:[...]
    },
    {
      <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x5176;&#x5B83;&#x7BA1;&#x7406;&quot;</span>,
      <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-slideshare&quot;</span>,
      <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;&quot;</span>,
      <span class="hljs-string">&quot;target&quot;</span>: <span class="hljs-string">&quot;_self&quot;</span>,
      <span class="hljs-string">&quot;child&quot;</span>:[...]
    }
  ]
}
</code></pre>
<h1 id="&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><a name="&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;" class="anchor-navigation-ex-anchor" href="#&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x7F13;&#x5B58;&#x6E05;&#x7406;&#x63A5;&#x53E3;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</h1>
<blockquote>
<p>  &#x8FD4;&#x56DE;&#x53C2;&#x6570;&#x5BF9;&#x5E94;&#x7684;&#x4E8B;&#x4F8B;(code&#xFF1A;0&#xFF0C;&#x6E05;&#x9664;&#x7F13;&#x5B58;&#x5931;&#x8D25;&#xFF1B;code&#xFF1A;1&#xFF0C;&#x8868;&#x793A;&#x6E05;&#x9664;&#x7F13;&#x5B58;&#x6210;&#x529F;&#xFF1B;)</p>
</blockquote>
<pre><code class="lang-json">   {
     <span class="hljs-string">&quot;code&quot;</span>: <span class="hljs-number">1</span>,
     <span class="hljs-string">&quot;msg&quot;</span>: <span class="hljs-string">&quot;&#x6E05;&#x9664;&#x670D;&#x52A1;&#x7AEF;&#x7F13;&#x5B58;&#x6210;&#x529F;&quot;</span>
   }
</code></pre>
<h1 id="&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;"><a name="&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;" class="anchor-navigation-ex-anchor" href="#&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;</h1>
<ul>
<li>&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x7684;&#x9875;&#x9762;&#xFF0C;&#x8BF7;&#x53C2;&#x8003;&#x4E0B;&#x65B9;&#x4EE3;&#x7801;&#x3002;&#xFF08;&#x5907;&#x6CE8;&#xFF1A;&#x9700;&#x8981;&#x5F15;&#x5165;miniPage.js&#x6587;&#x4EF6;&#xFF09;</li>
<li>&#x53C2;&#x6570;&#x8BF4;&#x660E;&#xFF08;layuimini-content-href=&#xFF1A;&#x9875;&#x9762;&#x94FE;&#x63A5;&#xFF0C;data-title&#xFF1A;&#x6807;&#x9898;&#xFF09;</li>
<li>&#x8C03;&#x7528;&#x65B9;&#x6CD5;&#x8FDB;&#x884C;&#x76D1;&#x542C;&#xFF1A;<code>miniPage.listen();</code>(&#x5907;&#x6CE8;&#xFF1A;&#x6846;&#x67B6;&#x521D;&#x59CB;&#x5316;&#x65F6;&#x5DF2;&#x7ECF;&#x8FDB;&#x884C;&#x76D1;&#x542C;&#xFF0C;&#x4E00;&#x822C;&#x60C5;&#x51B5;&#x4E0B;&#x4E0D;&#x9700;&#x8981;&#x518D;&#x6B21;&#x64CD;&#x4F5C;)</li>
<li>&#x793A;&#x4F8B;&#x5728;<code>page/welcome-1.html</code>&#x9875;&#x9762;&#x4E2D;&#x6709;</li>
</ul>
<pre><code class="lang-html">
    <span class="hljs-tag">&lt;<span class="hljs-name">a</span> <span class="hljs-attr">href</span>=<span class="hljs-string">&quot;javascript:;&quot;</span> <span class="hljs-attr">layuimini-content-href</span>=<span class="hljs-string">&quot;page/user-setting.html&quot;</span> <span class="hljs-attr">data-title</span>=<span class="hljs-string">&quot;&#x57FA;&#x672C;&#x8D44;&#x6599;&quot;</span> &gt;</span>&#x57FA;&#x672C;&#x8D44;&#x6599;<span class="hljs-tag">&lt;/<span class="hljs-name">a</span>&gt;</span>
</code></pre>
<h1 id="&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;"><a name="&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;" class="anchor-navigation-ex-anchor" href="#&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5728;&#x5185;&#x5BB9;&#x9875;&#x9762;&#x4E2D;&#x8FD4;&#x56DE;&#x4E3B;&#x9875;</h1>
<ul>
<li>&#x65B9;&#x6CD5;&#x4E00;&#xFF1A;&#x6DFB;&#x52A0;class&#x6837;&#x5F0F;layuimini-back-home<code>&lt;a lay-href=&quot;&quot; href=&quot;javascript:;&quot; class=&quot;layuimini-back-home&quot;&gt;&#x9996;&#x9875;&lt;/a&gt;</code></li>
<li>&#x65B9;&#x6CD5;&#x4E8C;&#xFF1A;<code>miniPage.hashHome();</code>&#xFF0C;&#x793A;&#x4F8B;&#x5728;<code>user-password.html</code>,<code>user-setting.html</code>&#x9875;&#x9762;&#x4E2D;&#x90FD;&#x6709;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    layui.use([<span class="hljs-string">&apos;form&apos;</span>,<span class="hljs-string">&apos;miniPage&apos;</span>], <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
        <span class="hljs-keyword">var</span> form = layui.form,
            layer = layui.layer,
            miniPage = layui.miniPage;

        <span class="hljs-comment">/**
         * &#x521D;&#x59CB;&#x5316;&#x8868;&#x5355;&#xFF0C;&#x8981;&#x52A0;&#x4E0A;&#xFF0C;&#x4E0D;&#x7136;&#x5237;&#x65B0;&#x90E8;&#x5206;&#x7EC4;&#x4EF6;&#x53EF;&#x80FD;&#x4F1A;&#x4E0D;&#x52A0;&#x8F7D;
         */</span>
        form.render();

        <span class="hljs-comment">//&#x76D1;&#x542C;&#x63D0;&#x4EA4;</span>
        form.on(<span class="hljs-string">&apos;submit(saveBtn)&apos;</span>, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">data</span>) </span>{
            <span class="hljs-keyword">var</span> index = layer.alert(<span class="hljs-built_in">JSON</span>.stringify(data.field), {
                <span class="hljs-attr">title</span>: <span class="hljs-string">&apos;&#x6700;&#x7EC8;&#x7684;&#x63D0;&#x4EA4;&#x4FE1;&#x606F;&apos;</span>
            }, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
                layer.close(index);
                miniPage.hashHome();
            });
            <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
        });

    });
</code></pre>
<h1 id="&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><a name="&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;" class="anchor-navigation-ex-anchor" href="#&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;</h1>
<ul>
<li>&#x7CFB;&#x7EDF;&#x5DF2;&#x5185;&#x7F6E;12&#x5957;&#x4E3B;&#x9898;&#x914D;&#x8272;&#xFF0C;&#x5982;&#x679C;&#x9700;&#x8981;&#x81EA;&#x5B9A;&#x4E49;&#x76AE;&#x80A4;&#x914D;&#x8272;&#xFF0C;&#x8BF7;&#x5728;<code>miniTheme.bgColorConfig</code>&#x65B9;&#x6CD5;&#x5185;&#x6309;&#x76F8;&#x540C;&#x683C;&#x5F0F;&#x6DFB;&#x52A0;&#x3002;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    <span class="hljs-keyword">var</span> bgColorConfig = [
                {
                    <span class="hljs-attr">headerRight</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
                    <span class="hljs-attr">headerRightThis</span>: <span class="hljs-string">&apos;#197971&apos;</span>,
                    <span class="hljs-attr">headerLogo</span>: <span class="hljs-string">&apos;#243346&apos;</span>,
                    <span class="hljs-attr">menuLeft</span>: <span class="hljs-string">&apos;#2f4056&apos;</span>,
                    <span class="hljs-attr">menuLeftThis</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
                    <span class="hljs-attr">menuLeftHover</span>: <span class="hljs-string">&apos;#3b3f4b&apos;</span>,
                    <span class="hljs-attr">tabActive</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
                },
                {
                    <span class="hljs-attr">headerRight</span>: <span class="hljs-string">&apos;#23262e&apos;</span>,
                    <span class="hljs-attr">headerRightThis</span>: <span class="hljs-string">&apos;#0c0c0c&apos;</span>,
                    <span class="hljs-attr">headerLogo</span>: <span class="hljs-string">&apos;#0c0c0c&apos;</span>,
                    <span class="hljs-attr">menuLeft</span>: <span class="hljs-string">&apos;#23262e&apos;</span>,
                    <span class="hljs-attr">menuLeftThis</span>: <span class="hljs-string">&apos;#737373&apos;</span>,
                    <span class="hljs-attr">menuLeftHover</span>: <span class="hljs-string">&apos;#3b3f4b&apos;</span>,
                    <span class="hljs-attr">tabActive</span>: <span class="hljs-string">&apos;#23262e&apos;</span>,
                }
    ];
</code></pre>
<h1 id="&#x5E38;&#x89C1;&#x95EE;&#x9898;"><a name="&#x5E38;&#x89C1;&#x95EE;&#x9898;" class="anchor-navigation-ex-anchor" href="#&#x5E38;&#x89C1;&#x95EE;&#x9898;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5E38;&#x89C1;&#x95EE;&#x9898;</h1>
<ul>
<li><font color="red">&#x4FEE;&#x6539;js&#x540E;&#x5237;&#x65B0;&#x9875;&#x9762;&#x672A;&#x751F;&#x6548;&#xFF0C;&#x8BF7;&#x5C1D;&#x8BD5;&#x6E05;&#x9664;&#x6D4F;&#x89C8;&#x5668;&#x7F13;&#x5B58;&#x3002;</font></li>
<li>IIS&#x73AF;&#x5883;&#x4E0B;&#x8BF7;&#x914D;&#x7F6E;&#x652F;&#x6301;&#x89E3;&#x6790;<code>.json</code>&#x683C;&#x5F0F;&#x6587;&#x4EF6;</li>
</ul>
<h1 id="&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><a name="&#x5907;&#x6CE8;&#x4FE1;&#x606F;" class="anchor-navigation-ex-anchor" href="#&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5907;&#x6CE8;&#x4FE1;&#x606F;</h1>
<ul>
<li>&#x83DC;&#x5355;&#x680F;&#x5EFA;&#x8BAE;&#x6700;&#x591A;&#x56DB;&#x7EA7;&#x83DC;&#x5355;&#xFF0C;&#x56DB;&#x7EA7;&#x4EE5;&#x540E;&#x83DC;&#x5355;&#x663E;&#x793A;&#x5E76;&#x6CA1;&#x6709;&#x90A3;&#x4E48;&#x53CB;&#x597D;&#x3002; </li>
</ul>
<footer class="page-footer"><span class="copyright">&#xA9; zhongshaofa all right reserved&#xFF0C;powered by Gitbook</span><span class="footer-modification">&#x6587;&#x4EF6;&#x4FEE;&#x8BA2;&#x65F6;&#x95F4;&#xFF1A;
2021-04-06 22:10:57
</span></footer>
<script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="iframe-v2.html#常见问题" class="navigation navigation-prev " aria-label="Previous page: 常见问题">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="onepage-v2.html#更新说明" class="navigation navigation-next " aria-label="Next page: 更新说明">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"单页版","level":"1.3.2","depth":2,"next":{"title":"更新说明","level":"*******","depth":3,"anchor":"#更新说明","path":"onepage-v2.md","ref":"onepage-v2.md#更新说明","articles":[]},"previous":{"title":"常见问题","level":"********","depth":3,"anchor":"#常见问题","path":"iframe-v2.md","ref":"iframe-v2.md#常见问题","articles":[]},"dir":"ltr"},"config":{"plugins":["anchor-nav-x","tbfed-pagefooter","search-plus","popup","highlight","image-captions","styled-blockquotes","expandable-chapters-interactive","hide-element","code","insert-logo","custom-favicon","github","github-buttons","donate","baidu-tongji"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"&copy zhongshaofa","modify_label":"文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"github":{"url":"https://github.com/zhongshaofa/layuimini"},"baidu-tongji":{"url":"https://hm.baidu.com/hm.js","token":"5637e63b6bd6247c36cf245293f121f9"},"search":{},"styled-blockquotes":{},"popup":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"donate":{"alipay":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/ali_pay.png","alipayText":"支付宝打赏","button":"打赏","title":"","wechat":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/wechat_pay.png","wechatText":"微信打赏"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"favicon":"images/favicon.ico","expandable-chapters-interactive":{},"anchor-nav-x":{"associatedWithSummary":true,"float":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showLevel":true},"github-buttons":{"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]},"custom-favicon":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"insert-logo":{"style":"background: none; max-height: 60px; min-height: 60px","url":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/logo_2.png"},"search-plus":{},"image-captions":{"variable_name":"_pictures"}},"theme":"default","author":"zhongshaofa","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[{"backlink":"index.html#fig1.1.1","level":"1.1","list_caption":"Figure: Image text","alt":"Image text","nro":1,"url":"./images/home.png","index":1,"caption_template":"Figure: _CAPTION_","label":"Image text","attributes":{},"skip":false,"key":"1.1.1"}]},"title":"layuimini开发手册","language":"zh-hans","gitbook":"*","sharing":{"qq":false,"all":["google","facebook","weibo","twitter","qq","qzone","linkedin","pocket"],"douban":false,"facebook":false,"weibo":false,"instapaper":false,"whatsapp":false,"hatenaBookmark":false,"twitter":false,"messenger":false,"line":false,"vk":false,"pocket":false,"google":false,"viber":false,"stumbleupon":false,"qzone":false,"linkedin":false},"description":"layuimini详细开发文档，最简单好用的后台模板。"},"file":{"path":"onepage-v2.md","mtime":"2021-04-06T14:10:57.915Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-04-06T14:17:05.226Z"},"basePath":".","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="gitbook/gitbook.js"></script>
    <script src="gitbook/theme.js"></script>
    
        
        <script src="gitbook/gitbook-plugin-anchor-nav-x/lib/handler.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-hide-element/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-code/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-insert-logo/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github-buttons/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-donate/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-baidu-tongji/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search-engine.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/lunr.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/search-lunr.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

