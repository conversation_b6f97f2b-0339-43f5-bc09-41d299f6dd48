
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Java示例(spring) · layuimini开发手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="zhongshaofa">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-anchor-nav-x/style/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-tbfed-pagefooter/footer.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-image-captions/image-captions.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-code/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-insert-logo/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-donate/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="netcore.html" />
    
    
    <link rel="prev" href="golang.html" />
    

    <style>
    @media only screen and (max-width: 640px) {
        .book-header .hidden-mobile {
            display: none;
        }
    }
    </style>
    <script>
        window["gitbook-plugin-github-buttons"] = {"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]};
    </script>

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" >
            
                <span>
            
                    
                    初始化接口后端示例(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="sql.html">
            
                <a href="sql.html">
            
                    
                    数据库结构示例
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="laravel.html">
            
                <a href="laravel.html">
            
                    
                    PHP示例(Laravel)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="thinkphp.html">
            
                <a href="thinkphp.html">
            
                    
                    PHP示例(ThinkPHP)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.4" data-path="golang.html">
            
                <a href="golang.html">
            
                    
                    Golang示例(beego)
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="1.2.5" data-path="java.html">
            
                <a href="java.html">
            
                    
                    Java示例(spring)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.6" data-path="netcore.html">
            
                <a href="netcore.html">
            
                    
                    ASP.NET CORE WebApi示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" >
            
                <span>
            
                    
                    使用说明(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1.1" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.2" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.3" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.4" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.5" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.6" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在页面中弹出新的Tab窗口（标签）">
            
                    
                    在页面中弹出新的Tab窗口（标签）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.7" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在页面中弹出新的Tab窗口（JS方法）">
            
                    
                    在页面中弹出新的Tab窗口（JS方法）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.8" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.9" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.10" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html">
            
                    
                    单页版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.3" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.4" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.5" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.6" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#在页面中打开新的页面">
            
                    
                    在页面中打开新的页面
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.7" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#在内容页面中返回主页">
            
                    
                    在内容页面中返回主页
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.8" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.9" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" >
            
                <span>
            
                    
                    使用说明(V1版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../iframe.html">
            
                <a href="../iframe.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1.1" data-path="../iframe.html">
            
                <a href="../iframe.html#默认配置说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.2" data-path="../iframe.html">
            
                <a href="../iframe.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.3" data-path="../iframe.html">
            
                <a href="../iframe.html#初始化api地址返回的参数说明">
            
                    
                    初始化api地址返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.4" data-path="../iframe.html">
            
                <a href="../iframe.html#在页面中弹出新的Tab窗口">
            
                    
                    在页面中弹出新的Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.5" data-path="../iframe.html">
            
                <a href="../iframe.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.6" data-path="../iframe.html">
            
                <a href="../iframe.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.7" data-path="../iframe.html">
            
                <a href="../iframe.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../onepage.html">
            
                <a href="../onepage.html">
            
                    
                    单页版
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Java示例(spring)</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-minus-circle"></i><ul><li><span class="title-icon "></span><a href="#java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;"><b></b>java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;</a></li></ul></div><a href="#java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><h1 id="java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;"><a name="java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;" class="anchor-navigation-ex-anchor" href="#java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;"><i class="fa fa-link" aria-hidden="true"></i></a>java&#x52A8;&#x6001;&#x751F;&#x6210;&#x521D;&#x59CB;&#x5316;&#x6570;&#x636E;&#xFF08;spring&#x6846;&#x67B6;&#xFF09;</h1>
<h5 id="&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;&#x9999;&#x55B7;&#x55B7;&#x7684;&#x5982;&#x6B4C;"><a name="&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;&#x9999;&#x55B7;&#x55B7;&#x7684;&#x5982;&#x6B4C;" class="anchor-navigation-ex-anchor" href="#&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;&#x9999;&#x55B7;&#x55B7;&#x7684;&#x5982;&#x6B4C;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;&#x9999;&#x55B7;&#x55B7;&#x7684;&#x5982;&#x6B4C;</h5>
<blockquote>
<p>&#x5BF9;&#x5E94;&#x63A7;&#x5236;&#x5668; <code>controllers/IndexController.java</code></p>
</blockquote>
<pre><code>
@RestController
@RequestMapping(&quot;login&quot;)
public class LoginController {
    @Resource
    private SysMenuService sysMenuService;

    @GetMapping(&quot;/menu&quot;)
    public Map&lt;String, Object&gt; menu() {
        return sysMenuService.menu();
    }
}
</code></pre><blockquote>
<p>&#x5BF9;&#x5E94;Service&#x903B;&#x8F91;&#x5C42;  <code>service/SysLoginServiceImpl.java</code></p>
</blockquote>
<pre><code>@Service
public class SysMenuServiceImpl implements SysMenuService {
    @Resource
    private SysMenuRepository sysMenuRepository;
    @Override
    public Map&lt;String, Object&gt; menu() {
        Map&lt;String, Object&gt; map = new HashMap&lt;&gt;(16);
        Map&lt;String,Object&gt; home = new HashMap&lt;&gt;(16);
        Map&lt;String,Object&gt; logo = new HashMap&lt;&gt;(16);
        List&lt;SysMenu&gt; menuList = sysMenuRepository.findAllByStatusOrderBySort(true);
        List&lt;MenuVo&gt; menuInfo = new ArrayList&lt;&gt;();
        for (SysMenu e : menuList) {
            MenuVo menuVO = new MenuVo();
            menuVO.setId(e.getKey().getId());
            menuVO.setPid(e.getPid());
            menuVO.setHref(e.getKey().getHref());
            menuVO.setTitle(e.getKey().getTitle());
            menuVO.setIcon(e.getIcon());
            menuVO.setTarget(e.getTarget());
            menuInfo.add(menuVO);
        }
        map.put(&quot;menuInfo&quot;, TreeUtil.toTree(menuInfo, 0L));
       home.put(&quot;title&quot;,&quot;&#x9996;&#x9875;&quot;);
        home.put(&quot;href&quot;,&quot;/page/welcome-1&quot;);//&#x63A7;&#x5236;&#x5668;&#x8DEF;&#x7531;,&#x81EA;&#x884C;&#x5B9A;&#x4E49;
        logo.put(&quot;title&quot;,&quot;&#x540E;&#x53F0;&#x7BA1;&#x7406;&#x7CFB;&#x7EDF;&quot;);
        logo.put(&quot;image&quot;,&quot;/static/images/back.jpg&quot;);//&#x9759;&#x6001;&#x8D44;&#x6E90;&#x6587;&#x4EF6;&#x8DEF;&#x5F84;,&#x53EF;&#x4F7F;&#x7528;&#x9ED8;&#x8BA4;&#x7684;logo.png 
        map.put(&quot;homeInfo&quot;, &quot;{title: &apos;&#x9996;&#x9875;&apos;,href: &apos;/ruge-web-admin/page/welcome.html&apos;}}&quot;);
        map.put(&quot;logoInfo&quot;, &quot;{title: &apos;RUGE ADMIN&apos;,image: &apos;images/logo.png&apos;}&quot;);
        return map;
    }
}
</code></pre><blockquote>
<p>TreeUtil<code>util/TreeUtil.java</code></p>
</blockquote>
<pre><code>public class TreeUtil {

    public static List&lt;MenuVo&gt; toTree(List&lt;MenuVo&gt; treeList, Long pid) {
        List&lt;MenuVo&gt; retList = new ArrayList&lt;MenuVo&gt;();
        for (MenuVo parent : treeList) {
            if (pid.equals(parent.getPid())) {
                retList.add(findChildren(parent, treeList));
            }
        }
        return retList;
    }
    private static MenuVo findChildren(MenuVo parent, List&lt;MenuVo&gt; treeList) {
        for (MenuVo child : treeList) {
            if (parent.getId().equals(child.getPid())) {
                if (parent.getChild() == null) {
                    parent.setChild(new ArrayList&lt;&gt;());
                }
                parent.getChild().add(findChildren(child, treeList));
            }
        }
        return parent;
    }
}
</code></pre><blockquote>
<p>repository&#x5C42; <code>reposiroty/SysMenuRepository.java</code></p>
</blockquote>
<pre><code>public interface SysMenuRepository extends JpaRepository&lt;SysMenu, Long&gt; {

    //&#x8FD9;&#x91CC;&#x6211;&#x53EA;&#x67E5;&#x8BE2;&#x9875;&#x9762;&#x8F6C;&#x6001;&#x4E3A;&#x542F;&#x7528;,&#x53EF;&#x81EA;&#x884C;&#x5B9A;&#x4E49;&#x548C;&#x5199;
    @Query(value = &quot;select * from  permission_security.system_menu where STATUS = 1  ORDER BY  sort &quot;,nativeQuery = true)
    public List&lt;SystemMenu&gt; getSystemMenuByStatusAndSort(Long status,Integer sort);

    List&lt;SysMenu&gt; findAllByStatusOrderBySort(Boolean status);
}
</code></pre><blockquote>
<p>entity&#x5C42; <code>entity/MenuEntity.java</code></p>
</blockquote>
<pre><code>@Getter
@Setter
@Embeddable
public class MenuKey implements Serializable {
    private Long id;
    private String title;
    private String href;
}



@Getter
@Setter
@Entity
@Table(name = &quot;system_menu&quot;)
public class SysMenu implements Serializable {
  // &#x590D;&#x5408;&#x4E3B;&#x952E;&#x8981;&#x7528;&#x8FD9;&#x4E2A;&#x6CE8;&#x89E3;
    @EmbeddedId
    private MenuKey key;
    private Long pid;
    private String icon;
    private String target;
    private Integer sort;
    private Boolean status;
    private String remark;
     @CreatedDate
    private Date create_at;
     @CreatedDate
    private Date update_at;
    private Date delete_at;
}


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuVo {
    private Long id;

    private Long pid;

    private String title;

    private String icon;

    private String href;

    private String target;

    private List&lt;MenuVo&gt; child;
}

//&#x8FD9;&#x91CC;&#x662F;&#x5E38;&#x89C4;&#x5199;&#x6CD5;&#x4E0D;&#x4F7F;&#x7528;lombok

/**
 *  insert&#x548C;update&#x6CE8;&#x89E3;,&#x89E3;&#x51B3;&#x65B0;&#x589E;&#x548C;&#x66F4;&#x65B0;&#x7684;&#x65F6;&#x5019;&#x6CA1;&#x6709;&#x4F7F;&#x7528;&#x9ED8;&#x8BA4;&#x503C;
 * <AUTHOR> 
 * @Date 2020-04-25
 * @Description   name =&quot;&#x8868;&quot; &#x5220;&#x6389;permission_security. schema=&quot;&quot;&#x53EF;&#x5220;&#x6389;&#x4E0D;&#x5FC5;&#x8DDF;&#x6211;&#xFF0C;&#x524D;&#x63D0;&#x662F;&#x4F60;&#x7684;&#x8868;&#x5BF9;&#x5E94;&#x4F60;&#x7684;&#x8868;&#x7A7A;&#x95F4;&#x5C31;&#x662F;&#x6570;&#x636E;&#x5E93;
 */

@Entity
@Table ( name =&quot;permission_security.system_menu&quot; , schema = &quot;root&quot;)
@DynamicInsert
@DynamicUpdate
public class SystemMenu  implements Serializable {

    private static final long serialVersionUID =  5421757630121636006L;

    /**&#x590D;&#x5408;&#x4E3B;&#x952E;&#x8981;&#x7528;&#x8FD9;&#x4E2A;&#x6CE8;&#x89E3;*/
    @EmbeddedId
    private MenuKey key;


    /**
     * &#x7236;ID
     */
       @Column(name = &quot;pid&quot; )
    private Long pid;



    /**
     * &#x83DC;&#x5355;&#x56FE;&#x6807;
     */
       @Column(name = &quot;icon&quot;)
    private String icon;

    /**
     * &#x94FE;&#x63A5;&#x6253;&#x5F00;&#x65B9;&#x5F0F;
     */
       @Column(name = &quot;target&quot;,columnDefinition = &quot;_self&quot;)
    private String target;
    /**
     * &#x83DC;&#x5355;&#x6392;&#x5E8F;
     */
       @Column(name = &quot;sort&quot; )
    private Long sort;

    /**
     * &#x72B6;&#x6001;(0:&#x7981;&#x7528;,1:&#x542F;&#x7528;)
     */
       @Column(name = &quot;status&quot;,columnDefinition = &quot;tinyint DEFAULT 1&quot;)
    private Integer status;

    /**
     * &#x5907;&#x6CE8;&#x4FE1;&#x606F;
     */
       @Column(name = &quot;remark&quot; )
    private String remark;

    /**
     * &#x521B;&#x5EFA;&#x65F6;&#x95F4;
     */
       @Column(name = &quot;create_at&quot; )
    private Date createAt;

    /**
     * &#x66F4;&#x65B0;&#x65F6;&#x95F4;
     */
       @Column(name = &quot;update_at&quot; )
    private Date updateAt;

    /**
     * &#x5220;&#x9664;&#x65F6;&#x95F4;
     */
       @Column(name = &quot;delete_at&quot; )
    private Date deleteAt;

/*    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }*/

    public Long getPid() {
        return this.pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }



    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }



    public String getTarget() {
        return this.target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public Long getSort() {
        return this.sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateAt() {
        return this.createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Date getUpdateAt() {
        return this.updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public Date getDeleteAt() {
        return this.deleteAt;
    }

    public void setDeleteAt(Date deleteAt) {
        this.deleteAt = deleteAt;
    }

    public MenuKey getKey() {
        return key;
    }

    public void setKey(MenuKey key) {
        this.key = key;
    }

}


@Embeddable
public class MenuKey implements Serializable {


    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = &quot;id&quot;)
    private Long id;
    /**
     * &#x540D;&#x79F0;
     */
    @Column(name = &quot;title&quot;)
    private String title;
    /**
     * &#x94FE;&#x63A5;
     */
    @Column(name = &quot;href&quot;)
    private String href;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuVo {

    private Long id;

    private Long pid;

    private String title;

    private String icon;

    private String href;

    private String target;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public List&lt;MenuVo&gt; getChild() {
        return child;
    }

    public void setChild(List&lt;MenuVo&gt; child) {
        this.child = child;
    }

    private List&lt;MenuVo&gt; child;
}
</code></pre><blockquote>
<p>yml&#x914D;&#x7F6E;&#x6587;&#x4EF6; <code>resource/application.yml</code></p>
</blockquote>
<pre><code>#&#x672C;&#x5730;&#x5F00;&#x53D1;&#x73AF;&#x5883;&#x914D;&#x7F6E;&#x4E2D;&#x5FC3;

spring:
  application:
    name: springboot-webAdmin
  jpa:
    show-sql: true
    database: mysql
    #generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL5Dialect
    hibernate:
      naming:
          #&#x89E3;&#x51B3;&#x4F7F;&#x7528;&#x5176;&#x4ED6;&#x5E93;&#x7684;&#x8868;&#x65F6;&#x5019;&#xFF0C;&#x628A;&#x5C0F;&#x6570;&#x70B9;&#x53D8;&#x6210;&#x4E0B;&#x5212;&#x7EBF;&#xFF0C;&#x5BFC;&#x81F4;sql&#x65E0;&#x6CD5;&#x6210;&#x529F;&#x6267;&#x884C;&#x3002;
          #&#x8FD9;&#x662F;&#x7531;&#x4E8E;&#x7269;&#x7406;&#x547D;&#x540D;&#x7B56;&#x7565;&#x5F15;&#x8D77;&#x7684;&#xFF0C;&#x5927;&#x5199;&#x5B57;&#x6BCD;&#x53D8;&#x5C0F;&#x5199;&#xFF0C;&#x52A0;_&#x4E0B;&#x5212;&#x7EBF;&#xFF08;hibernate5&#x4EE5;&#x4E0A;&#x9AD8;&#x7248;&#x672C;&#xFF09;
       physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      #ddl-auto: update
    #ddl-auto: update
  datasource:
    url: ***********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      minimum-idle: 2
      idle-timeout: 60000
      connection-timeout: 30000
      max-lifetime: 1800000
      pool-name: DatebookHikariCP
  #thymeleaf&#x6A21;&#x677F;&#x914D;&#x7F6E;
  thymeleaf:
    cache: false
    enabled: true
    prefix: classpath:/templates/
    suffix: .html
    #&#x4E25;&#x683C;&#x6267;&#x884C;H5&#x6807;&#x51C6;
    mode: LEGACYHTML5
    encoding: UTF-8
    servlet:
      content-type: text/html
  #content-type: text/html
  resources:
    chain:
      strategy:
        content:
          enabled: true
          paths: /**

  #&#x9759;&#x6001;&#x8D44;&#x6E90;&#x8DEF;&#x5F84;
  mvc:
    static-path-pattern: /static/**
    #&#x5173;&#x6389;&#x539F;&#x751F;icon&#x56FE;&#x6807;
    favicon:
      enabled: false

#&#x9879;&#x76EE;&#x7AEF;&#x53E3;
server:
  port: 8080
  #&#x8FDE;&#x63A5;&#x8D85;&#x65F6;&#xFF0C;&#x5355;&#x4F4D;&#x4E3A;&#x6BEB;&#x79D2;&#xFF0C;-1&#x6C38;&#x4E0D;&#x8D85;&#x65F6;
  connection-timeout: 60000
  #&#x8BBE;&#x7F6E;tomcat&#x53C2;&#x6570;
  tomcat:
    uri-encoding: utf-8
    max-connections: 10000
    min-spare-threads: 10
    #&#x6700;&#x5927;220&#x4E2A;&#x5E76;&#x53D1;&#xFF0C;&#x53EF;&#x4EE5;&#x8FBE;&#x5230;&#x4E0D;&#x4E22;&#x5305;&#xFF08;&#x53EF;&#x4EE5;&#x81EA;&#x5DF1;&#x5B9E;&#x6D4B;&#xFF09;&#xFF0C;&#x9ED8;&#x8BA4;&#x4E3A;200&#x3002;
    max-threads: 220
  #&#x914D;&#x7F6E;&#x8BBF;&#x95EE;&#x8DEF;&#x5F84;&#xFF0C;&#x9ED8;&#x8BA4;&#x4E3A;/
  #servlet:
    #context-path: /index/main



#&#x914D;&#x7F6E;&#x65E5;&#x5FD7;&#x6587;&#x4EF6;&#x53C2;&#x6570;
logging:
  file:
    path: F:/myLog/adminLog.log
  level:
    org:
      springframework: debug
    hibernate: debug
</code></pre><footer class="page-footer"><span class="copyright">&#xA9; zhongshaofa all right reserved&#xFF0C;powered by Gitbook</span><span class="footer-modification">&#x6587;&#x4EF6;&#x4FEE;&#x8BA2;&#x65F6;&#x95F4;&#xFF1A;
2021-04-06 22:10:57
</span></footer>
<script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="golang.html" class="navigation navigation-prev " aria-label="Previous page: Golang示例(beego)">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="netcore.html" class="navigation navigation-next " aria-label="Next page: ASP.NET CORE WebApi示例">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Java示例(spring)","level":"1.2.5","depth":2,"next":{"title":"ASP.NET CORE WebApi示例","level":"1.2.6","depth":2,"path":"init/netcore.md","ref":"init/netcore.md","articles":[]},"previous":{"title":"Golang示例(beego)","level":"1.2.4","depth":2,"path":"init/golang.md","ref":"init/golang.md","articles":[]},"dir":"ltr"},"config":{"plugins":["anchor-nav-x","tbfed-pagefooter","search-plus","popup","highlight","image-captions","styled-blockquotes","expandable-chapters-interactive","hide-element","code","insert-logo","custom-favicon","github","github-buttons","donate","baidu-tongji"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"&copy zhongshaofa","modify_label":"文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"github":{"url":"https://github.com/zhongshaofa/layuimini"},"baidu-tongji":{"url":"https://hm.baidu.com/hm.js","token":"5637e63b6bd6247c36cf245293f121f9"},"search":{},"styled-blockquotes":{},"popup":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"donate":{"alipay":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/ali_pay.png","alipayText":"支付宝打赏","button":"打赏","title":"","wechat":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/wechat_pay.png","wechatText":"微信打赏"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"favicon":"images/favicon.ico","expandable-chapters-interactive":{},"anchor-nav-x":{"associatedWithSummary":true,"float":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showLevel":true},"github-buttons":{"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]},"custom-favicon":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"insert-logo":{"style":"background: none; max-height: 60px; min-height: 60px","url":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/logo_2.png"},"search-plus":{},"image-captions":{"variable_name":"_pictures"}},"theme":"default","author":"zhongshaofa","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[{"backlink":"index.html#fig1.1.1","level":"1.1","list_caption":"Figure: Image text","alt":"Image text","nro":1,"url":"./images/home.png","index":1,"caption_template":"Figure: _CAPTION_","label":"Image text","attributes":{},"skip":false,"key":"1.1.1"}]},"title":"layuimini开发手册","language":"zh-hans","gitbook":"*","sharing":{"qq":false,"all":["google","facebook","weibo","twitter","qq","qzone","linkedin","pocket"],"douban":false,"facebook":false,"weibo":false,"instapaper":false,"whatsapp":false,"hatenaBookmark":false,"twitter":false,"messenger":false,"line":false,"vk":false,"pocket":false,"google":false,"viber":false,"stumbleupon":false,"qzone":false,"linkedin":false},"description":"layuimini详细开发文档，最简单好用的后台模板。"},"file":{"path":"init/java.md","mtime":"2021-04-06T14:10:57.911Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-04-06T14:17:05.226Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-anchor-nav-x/lib/handler.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-hide-element/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-code/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-insert-logo/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-github/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-github-buttons/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-donate/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-baidu-tongji/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search/search-engine.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-lunr/lunr.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-lunr/search-lunr.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

