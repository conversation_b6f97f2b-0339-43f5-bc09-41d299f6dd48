@-webkit-keyframes select-input-upbit {
    from {
        -webkit-transform: translate3d(0, 30px, 0);
        opacity: 0.3;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes select-input-upbit {
    from {
        transform: translate3d(0, 30px, 0);
        opacity: 0.3;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@-webkit-keyframes select-input-loader {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes select-input-loader {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}


.select-input {
    padding: 0;
    margin: 0;
}

.select-input .select-input-content {
    position: relative;
    width: 90%;
    margin: 20px auto;
}

.select-input .select-input-container {
    width: 100%;
    height: 36px;
    line-height: 36px;
    position: relative;
}

.select-input .select-input-input {
    outline: none;
    width: 100%;
    height: 100%;
    line-height: 100%;
    border: 1px solid #E6E6E6;
    font-size: 14px;
    border-radius: 2px;
    cursor: pointer;
    padding: 0 10px;
    box-sizing: border-box;
}

.select-input .select-input-input::-webkit-input-placeholder, .layui-select::-webkit-input-placeholder, .layui-textarea::-webkit-input-placeholder {
    line-height: 1.3;
    color: #e6e6e6;
}

.select-input-input:hover,
.select-input-input:focus {
    border: 1px solid #C0C4CC;
}

.select-input-icon {
    display: inline-block;
    overflow: hidden;
    position: absolute;
    width: 0;
    height: 0;
    right: 10px;
    top: 50%;
    margin-top: -1px;
    cursor: pointer;
    border: 6px dashed transparent;
    border-top-color: #C2C2C2;
    border-top-style: solid;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.select-input-icon-expand {
    margin-top: -8px;
    transform: rotate(180deg);
}

.select-input-body {
    position: absolute;
    left: 0;
    top: 42px;
    padding: 5px 0;
    z-index: 999;
    width: 100%;
    min-width: fit-content;
    box-sizing: border-box;
    border: 1px solid #E6E6E6;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 12%);
    animation-name: select-input-upbit;
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.select-input .scroll-body {
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 200px;
}

.select-input .scroll-body::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    height: 6px;
}

.select-input .scroll-body::-webkit-scrollbar-thumb {
    /*滚动条里面小方块样式*/
    border-radius: 100px;
    background: #999;
}

.select-input .scroll-body::-webkit-scrollbar-track {
    /*滚动条里面轨道样式*/
    /* -webkit-box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2); */
    border-radius: 0;
    background: #fff;
}

.select-input .select-input-option {
    align-items: center;
    position: relative;
    cursor: pointer;
}

.select-input .select-input-option-content {
    position: relative;
    padding-left: 15px;
    height: 36px;
    line-height: 36px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #666;
    font-size: 14px;
}

.select-input .select-input-option-content:hover {
    background-color: rgb(242, 242, 242)
}

.select-input .select-input-paging {
    padding: 0 10px;
    display: flex;
    margin-top: 5px;
}

.select-input .select-input-paging > span {
    height: 30px;
    line-height: 30px;
    display: flex;
    flex: 1;
    justify-content: center;
    vertical-align: middle;
    margin: 0 -1px 0 0;
    background-color: #fff;
    color: #333;
    font-size: 12px;
    border: 1px solid #e2e2e2;
    flex-wrap: nowrap;
    width: 100%;
    overflow: hidden;
    min-width: 50px;
    cursor: pointer;
}

.select-input .select-input-paging > span:first-child {
    border-radius: 2px 0 0 2px;
}

.select-input .select-input-paging > span:last-child {
    border-radius: 0 2px 2px 0;
}

.select-input .select-input-paging > span.select-input-no-drop {
    cursor: no-drop;
    color: rgb(210, 210, 210);
}

.select-input .select-input-label {
    position: absolute;
    top: 0;
    left: 0px;
    right: 30px;
    box-sizing: border-box;
    overflow: auto hidden;
}

.select-input .scroll {
    overflow-y: hidden;
}

.select-input .label-content {
    line-height: 30px;
    display: flex;
    padding: 3px 10px;
    flex-wrap: nowrap;
    white-space: nowrap;
    position: relative;
}

.select-input .dis {
    display: none;
}

.select-input .show {
    display: block;
}

.select-input .select-input-label-block {
    height: 26px;
    line-height: 26px;
    background-color: rgb(0, 150, 136);
    display: flex;
    position: relative;
    padding: 0px 24px 0 5px;
    margin: 3px 5px 3px 0;
    border-radius: 3px;
    align-items: baseline;
    color: #FFF;
    font-size: 14px;
}

.select-input .select-input-icon-close {
    position: absolute;
    top: 6px;
    right: 5px;
    width: 14px;
    height: 14px;
    overflow: hidden;
    cursor: pointer;
}

.select-input .select-input-icon-close::before,
.select-input .select-input-icon-close::after {
    content: '';
    position: absolute;
    height: 2px;
    width: 100%;
    top: 50%;
    left: 0;
    margin-top: -1px;
    background: #fff;
}

.select-input .select-input-icon-close::before {
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.select-input .select-input-icon-close::after {
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.select-input .select-input-icon-close.rounded::before,
.select-input .select-input-icon-close.rounded::after {
    border-radius: 5px;
}

/*loading*/

.select-input .select-input-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, .6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.select-input .select-input-loader {
    border: .2em dotted currentcolor;
    border-radius: 50%;
    -webkit-animation: 1s loader linear infinite;
    animation: 1s select-input-loader linear infinite;
    display: inline-block;
    width: 1em;
    height: 1em;
    color: inherit;
    vertical-align: middle;
    pointer-events: none;
}

.select-input .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgb(255, 255, 255);
}

.select-input .loadEffect {
    position: fixed;
    left: 50%;
    top: 50%;
    margin-top: -20px;
    margin-left: -20px;
    width: 40px;
    height: 40px;
}

.select-input .loadEffect div {
    width: 100%;
    height: 100%;
    position: absolute;
    -webkit-animation: load 1.48s linear infinite;
}

.select-input .loadEffect div span {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgb(0, 150, 136);
    position: absolute;
    left: 50%;
    margin-top: -10px;
    margin-left: -10px;
}

@-webkit-keyframes load {
    0% {
        -webkit-transform: rotate(0deg);
    }
    10% {
        -webkit-transform: rotate(45deg);
    }
    50% {
        opacity: 1;
        -webkit-transform: rotate(160deg);
    }
    62% {
        opacity: 0;
    }
    65% {
        opacity: 0;
        -webkit-transform: rotate(200deg);
    }
    90% {
        -webkit-transform: rotate(340deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

.select-input .loadEffect div:nth-child(1) {
    -webkit-animation-delay: 0.2s;
}

.select-input .loadEffect div:nth-child(2) {
    -webkit-animation-delay: 0.4s;
}

.select-input .loadEffect div:nth-child(3) {
    -webkit-animation-delay: 0.6s;
}

.select-input .loadEffect div:nth-child(4) {
    -webkit-animation-delay: 0.8s;
}
