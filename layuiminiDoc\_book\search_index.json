{"index": {"version": "0.5.12", "fields": [{"name": "title", "boost": 10}, {"name": "keywords", "boost": 15}, {"name": "body", "boost": 1}], "ref": "url", "documentStore": {"store": {"./": ["\"img\"", "&&", "04", "06", "2021", "22:10:57", "=", "===", "awesome图标库的完美支持。", "awesome图标选择插件", "b", "clone", "console.log(\"plugin", "e.target.tagnam", "figure:", "function(e){", "gitbook文件修订时间：", "gitee下载命令：git", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/mast", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/onepag", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2", "gitee发版地址：https://gitee.com/zhongshaofa/layuimini/releas", "github下载命令：git", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/mast", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/onepag", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2", "github发版地址：https://github.com/zhongshaofa/layuimini/releas", "https://gitee.com/zhongshaofa/layuimini", "https://github.com/zhongshaofa/layuimini", "ifram", "imag", "layuimini后台模板项目介绍主要特性代码仓库(ifram", "master", "onepag", "popup....\");document.onclick", "reserved，pow", "right", "text", "url地址hash定位，可以清楚看到当前tab的地址信息。", "v1版", "v2", "v2版", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "©", "一个接口几行代码而已直接初始化整个框架，无需复杂操作。", "下载方式", "主要特性", "代码仓库(ifram", "代码仓库(onepag", "刷新页面会保留当前的窗口，并且会定位当前窗口对应左侧菜单栏。", "加群请备注来源：如gitee、github、官网等。", "单页版", "单页版)", "单页版)下载方式效果预览捐赠支持layuimini后台模板", "发行版地址", "在线预览地址：http://layuimini.99php.cn/iframe/v1/index.html", "在线预览地址：http://layuimini.99php.cn/iframe/v2/index.html", "在线预览地址：http://layuimini.99php.cn/onepage/v1/index.html", "在线预览地址：http://layuimini.99php.cn/onepage/v2/index.html", "多tab版)", "多tab版)代码仓库(onepag", "失效以及报错菜单无法直接打开，并给出弹出层提示完美的线上用户体验。", "开源项目不易，若此项目能得到你的青睐，可以捐赠支持作者持续开发与维护，感谢所有支持开源的朋友。", "总体预览", "技术交流qq群：1165301500、667813249&#x1f235;、561838086&#x1f235;", "捐赠支持", "支持font", "支持多tab，可以打开多窗口。", "支持无限级菜单和对font", "效果预览", "最简洁、清爽、易用的layui后台框架模板。", "界面足够简洁清爽，响应式且适配手机端。", "简介", "页面支持多配色方案，可自行选择喜欢的配色。", "项目介绍", "项目会不定时进行更新，建议star和watch一份。"], "init/sql.html": ["\"img\"", "&&", "''", "'0'", "'1'", "'_self'", "'id',", "'创建时间',", "'删除时间',", "'名称',", "'备注信息',", "'更新时间',", "'父id',", "'状态(0:禁用,1:启用)',", "'菜单图标',", "'菜单排序',", "'链接',", "'链接打开方式',", "(", "(`href`)", "(`id`),", "(`title`),", ")", "04", "06", "2021", "22:10:57", "=", "===", "`create_at`", "`delete_at`", "`href`", "`icon`", "`id`", "`pid`", "`remark`", "`sort`", "`status`", "`system_menu`", "`target`", "`title`", "`update_at`", "auto_incr", "auto_increment=250", "charset=utf8", "comment", "comment='系统菜单表';", "console.log(\"plugin", "creat", "default", "e.target.tagnam", "engine=innodb", "function(e){", "gitbook文件修订时间：", "int(11)", "key", "null", "popup....\");document.onclick", "primari", "reserved，pow", "right", "row_format=compact", "tabl", "timestamp", "tinyint(1)", "unsign", "<PERSON><PERSON><PERSON>(100)", "<PERSON><PERSON><PERSON>(20)", "<PERSON><PERSON><PERSON>(255)", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "©", "后面php、go的动态生成示例都是基于该表结构", "数据库结构示例", "数据库表结构示例数据库表结构示例"], "init/laravel.html": ["\"img\"", "$child", "$child;", "$homeinfo,", "$logoinfo", "$logoinfo,", "$menuinfo", "$menuinfo,", "$menulist", "$menulist);", "$menulist){", "$menulist;", "$node", "$node;", "$node['child']", "$systeminit", "$thi", "$treelist", "$treelist;", "$treelist[]", "$v", "$v)", "&&", "'desc')", "'homeinfo'", "'href'", "'image'", "'images/logo.png',", "'layui", "'logoinfo'", "'menuinfo'", "'page/welcom", "'title'", "'首页',", "(!empty($child))", "($menulist", "($pid", "(array)$v;", "//", "//递归获取子菜单", "04", "06", "1)", "1.html?t=1',", "2021", "22:10:57", "=", "==", "===", "=>", ">buildmenuchild($v", ">buildmenuchild(0,", ">get();", ">getmenulist();", ">id,", ">json($systeminit);", ">orderby('sort',", ">pid)", ">select(['id','pid','title','icon','href','target'])", ">where('status',", "[", "[];", "];", "app/http/controllers/indexcontroller.php", "buildmenuchild($pid,", "console.log(\"plugin", "db::table('system_menu')", "e.target.tagnam", "foreach", "function", "function(e){", "getmenulist(){", "gitbook文件修订时间：", "laravel框架示例(6.2版本)laravel框架示例(6.2版本)", "mini',", "php示例(laravel)", "popup....\");document.onclick", "privat", "reserved，pow", "response()", "return", "right", "todo", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "©", "为了方便演示，直接用db类去写", "后续此处加上用户的权限判断", "获取菜单列表"], "init/thinkphp.html": ["\"img\"", "$child", "$child;", "$homeinfo,", "$logoinfo", "$logoinfo,", "$menuinfo", "$menuinfo,", "$menulist", "$menulist);", "$menulist){", "$menulist;", "$node", "$node;", "$node['child']", "$systeminit", "$thi", "$treelist", "$treelist;", "$treelist[]", "$v)", "$v;", "$v['pid'])", "&&", "'desc')", "'homeinfo'", "'href'", "'image'", "'images/logo.png',", "'layui", "'logoinfo'", "'menuinfo'", "'page/welcom", "'title'", "'首页',", "(!empty($child))", "($menulist", "($pid", "//", "//递归获取子菜单", "04", "06", "1)", "1.html?t=1',", "2021", "22:10:57", "=", "==", "===", "=>", ">buildmenuchild($v['id'],", ">buildmenuchild(0,", ">field('id,pid,title,icon,href,target')", ">getmenulist();", ">order('sort',", ">select();", ">where('status',", "[", "[];", "];", "app/index/controller/index.php", "buildmenuchild($pid,", "console.log(\"plugin", "db::name('system_menu')", "e.target.tagnam", "foreach", "function", "function(e){", "getmenulist(){", "gitbook文件修订时间：", "json($systeminit);", "mini',", "php示例(thinkphp)", "popup....\");document.onclick", "privat", "reserved，pow", "return", "right", "thinkphp框架示例(6.0版本)thinkphp框架示例(6.0版本)", "todo", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "©", "为了方便演示，直接用db类去写", "后续此处加上用户的权限判断", "获取菜单列表"], "init/golang.html": ["!=", "\"beegoadmin/models\"", "\"github.com/astaxie/beego\"", "\"github.com/astaxie/beego/orm\"", "\"images/logo.png\"", "\"img\"", "\"layui", "\"page/welcom", "\"time\"", "\"首页\"", "&&", "&menutreelist{", "(", "(c", "(m", ")", "*indexcontroller)", "*systemmenu)", "//", "//递归获取子菜单", "0", "04", "06", "1.html?t=1\"", "2021", "22:10:57", ":=", "=", "==", "===", "[]*menutreelist", "[]systemmenu", "[]systemmenu)", "_", "_,", "`json:\"child\"`", "`json:\"create_at\";orm:\"auto_now;type(datetime)\"`", "`json:\"homeinfo\"`", "`json:\"href\"`", "`json:\"icon\"`", "`json:\"id\"`", "`json:\"image\"`", "`json:\"logoinfo\"`", "`json:\"menuinfo\"`", "`json:\"pid\"`", "`json:\"remark\"`", "`json:\"sort\"`", "`json:\"status\"`", "`json:\"target\"`", "`json:\"title\"`", "append(treelist,", "beego.control", "buildmenuchild(pid", "c.data[\"json\"]", "<PERSON><PERSON>()", "child", "console.log(\"plugin", "control", "controllers/indexcontroller.go", "createat", "e.target.tagnam", "func", "function(e){", "getmenulist()", "getsysteminit()", "gitbook文件修订时间：", "golang动态生成初始化数据（beego框架）golang动态生成初始化数据（beego框架）", "golang示例(beego)", "homeinfo", "href", "href:", "icon", "icon:", "id", "id:", "imag", "import", "indexcontrol", "int", "int,", "len(child)", "logo", "logoinfo", "m.<PERSON>(0,", "m.get<PERSON>st()", "menuinfo", "menulist", "menulist)", "menutreelist", "mini\"", "model", "models/systemmenu.go", "new(models.systemmenu).getsysteminit()", "node", "node)", "node.child", "o", "o.querytable(m.tablename()).filter(\"status\",1).orderby(\"", "orm.new<PERSON>()", "packag", "pid", "pid:", "popup....\");document.onclick", "rang", "remark", "reserved，pow", "return", "right", "sort", "sort\").all(&menulist)", "statu", "string", "struct", "systeminit", "systeminit()", "systeminit.homeinfo.href", "systeminit.homeinfo.titl", "systeminit.logoinfo.imag", "systeminit.logoinfo.titl", "systeminit.menuinfo", "systemmenu", "tablename(\"system_menu\")", "tablename()", "target", "target:", "time.tim", "titl", "title:", "todo", "treelist", "type", "v", "v.buildmenuchild(v.id,", "v.href,", "v.icon,", "v.id,", "v.pid", "v.pid,", "v.target,", "v.title,", "var", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "©", "初始化后台框架接口", "初始化结构体", "后续此处加上用户的权限判断", "对应model", "对应控制器", "获取初始化数据", "获取菜单列表", "菜单", "菜单结构体", "首页"], "init/java.html": ["\",nativequeri", "\"_self\")", "\"create_at\"", "\"delete_at\"", "\"href\")", "\"icon\")", "\"id\")", "\"img\"", "\"pid\"", "\"remark\"", "\"root\")", "\"select", "\"sort\"", "\"status\",columndefinit", "\"system_menu\")", "\"target\",columndefinit", "\"tinyint", "\"title\")", "\"update_at\"", "\"{title:", "#content", "#context", "#ddl", "#gener", "#servlet:", "#thymeleaf模板配置", "#严格执行h5标准", "#关掉原生icon图标", "#最大220个并发，可以达到不丢包（可以自己实测），默认为200。", "#本地开发环境配置中心", "#解决使用其他库的表时候，把小数点变成下划线，导致sql无法成功执行。", "#设置tomcat参数", "#这是由于物理命名策略引起的，大写字母变小写，加_下划线（hibernate5以上高版本）", "#连接超时，单位为毫秒，", "#配置日志文件参数", "#配置访问路径，默认为/", "#静态资源路径", "#项目端口", "&&", "'/ruge", "'images/logo.png'}\");", "'ruge", "'首页',href:", "(", "(menuvo", "(parent.getchild()", "(parent.getid().equals(child.getpid()))", "(pid.equals(parent.getpid()))", "(sysmenu", ")", "*", "*/", ",", ".html", "/*", "/**", "/**复合主键要用这个注解*/", "//", "//这里我只查询页面转态为启用,可自行定义和写", "//这里是常规写法不使用lombok", "/index/main", "/static/**", "04", "06", "0l));", "1", "1\")", "1\");//控制器路由,自行定义", "10", "10000", "123456", "1800000", "1永不超时", "2", "2020", "2021", "220", "22:10:57", "25", "30000", "5421757630121636006l;", "60000", "8", "8&servertimezone=gmt%2b8", "8080", ":", "=", "=\"permission_security.system_menu\"", "=\"表\"", "==", "===", "@author", "@column(nam", "@createdd", "@data", "@date", "@descript", "@dynamicinsert", "@dynamicupd", "@embedd", "@embeddedid", "@entiti", "@generatedvalue(strategi", "@getmapping(\"/menu\")", "@getter", "@jsoninclude(jsoninclude.include.non_null)", "@overrid", "@query(valu", "@requestmapping(\"login\")", "@resourc", "@restcontrol", "@servic", "@setter", "@tabl", "@table(nam", "admin',image:", "admin/page/welcome.html'}}\");", "application:", "arraylist();", "arraylist<>());", "arraylist<>();", "auto", "auto:", "boolean", "cache:", "chain:", "child", "child)", "child;", "class", "classpath:/templates/", "com.mysql.jdbc.driv", "com.zaxxer.hikari.hikaridatasourc", "commit:", "connect", "connections:", "console.log(\"plugin", "content", "content:", "controllers/indexcontroller.java", "create_at;", "createat)", "createat;", "databas", "database:", "datasource:", "date", "datebookhikaricp", "ddl:", "debug", "default", "delete_at;", "deleteat)", "deleteat;", "driver", "e", "e.target.tagnam", "enabled:", "encoding:", "entity/menuentity.java", "entity层", "extend", "f:/mylog/adminlog.log", "fals", "favicon:", "file:", "final", "findallbystatusorderbysort(boolean", "findchildren(menuvo", "function(e){", "generationtype.identity)", "getchild()", "getcreateat()", "getdeleteat()", "gethref()", "geticon()", "getid()", "getkey()", "getpid()", "getremark()", "getsort()", "getstatus()", "getsystemmenubystatusandsort(long", "gettarget()", "gettitle()", "getupdateat()", "gitbook文件修订时间：", "hashmap<>(16);", "hibernate:", "hikari:", "home", "home.put(\"href\",\"/page/welcom", "home.put(\"title\",\"首页\");", "href)", "href;", "icon)", "icon;", "id)", "id;", "idl", "idle:", "implement", "insert和update注解,解决新增和更新的时候没有使用默认值", "integ", "interfac", "java动态生成初始化数据（spring框架）java动态生成初始化数据（spring框架）", "java示例(spring)", "*****************************************************************", "jpa:", "jparepositori", "key)", "key;", "legacyhtml5", "level:", "lifetime:", "list", "logging:", "logincontrol", "logo", "logo.put(\"image\",\"/static/images/back.jpg\");//静态资源文件路径,可使用默认的logo.png", "logo.put(\"title\",\"后台管理系统\");", "long", "map", "map.put(\"homeinfo\",", "map.put(\"logoinfo\",", "map.put(\"menuinfo\",", "map;", "max", "menu()", "menuinfo", "menuinfo.add(menuvo);", "menukey", "menulist", "menulist)", "menuvo", "menuvo();", "menuvo.sethref(e.getkey().gethref());", "menuvo.seticon(e.geticon());", "menuvo.setid(e.getkey().getid());", "menuvo.setpid(e.getpid());", "menuvo.settarget(e.gettarget());", "menuvo.settitle(e.getkey().gettitle());", "min", "minimum", "mode:", "mvc:", "mysql", "name", "name:", "naming:", "new", "null)", "order", "org.hibernate.boot.model.naming.physicalnamingstrategystandardimpl", "org.hibernate.dialect.mysql5dialect", "org:", "parent", "parent,", "parent.getchild().add(findchildren(child,", "parent.setchild(new", "parent;", "password:", "path", "path:", "paths:", "pattern:", "permission_security.system_menu", "physic", "pid)", "pid;", "platform:", "pool", "popup....\");document.onclick", "port:", "prefix:", "privat", "public", "remark)", "remark;", "reposiroty/sysmenurepository.java", "repository层", "reserved，pow", "resource/application.yml", "resources:", "retlist", "retlist.add(findchildren(parent,", "retlist;", "return", "right", "root", "schema", "schema=\"\"可删掉不必跟我，前提是你的表对应你的表空间就是数据库", "serializ", "serialversionuid", "server:", "service/sysloginserviceimpl.java", "servlet:", "setchild(list", "setcreateat(d", "setdeleteat(d", "sethref(str", "seticon(str", "setid(long", "setkey(menukey", "setpid(long", "setremark(str", "setsort(long", "setstatus(integ", "settarget(str", "settitle(str", "setupdateat(d", "show", "sort", "sort)", "sort);", "sort;", "spare", "spring:", "springboot", "springframework:", "sql:", "static", "statu", "status)", "status);", "status,integ", "status;", "strategy:", "string", "suffix:", "sysmenu", "sys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sysmenurepository.findallbystatusorderbysort(true);", "sysmenurepository;", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sysmenuservice.menu();", "sysmenuservice;", "sysmenuserviceimpl", "systemmenu", "target)", "target;", "text/html", "this.child", "this.createat", "this.createat;", "this.deleteat", "this.deleteat;", "this.href", "this.icon", "this.icon;", "this.id", "this.id;", "this.key", "this.pid", "this.pid;", "this.remark", "this.remark;", "this.sort", "this.sort;", "this.statu", "this.status;", "this.target", "this.target;", "this.titl", "this.updateat", "this.updateat;", "threads:", "thymeleaf:", "timeout:", "title)", "title;", "tomcat:", "totree(list", "treelist)", "treelist));", "treelist,", "treeutil", "treeutil.totree(menuinfo,", "treeutilutil/treeutil.java", "true", "true)", "type:", "updat", "update_at;", "updateat)", "updateat;", "uri", "url:", "username:", "utf", "void", "web", "webadmin", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "wupeng", "yml配置文件", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "}*/", "©", "创建时间", "删掉permission_security.", "删除时间", "名称", "备注信息", "复合主键要用这个注解", "对应service逻辑层", "对应控制器", "更新时间", "父id", "状态(0:禁用,1:启用)", "示例提供来源：香喷喷的如歌", "菜单图标", "菜单排序", "链接", "链接打开方式"], "init/netcore.html": ["!=", "\"\",", "\"\";", "\"_self\";", "\"images/logo.png\";", "\"img\"", "\"page/welcom", "\"sdsdsdsff\";", "\"根目录\",", "\"首页\";", "&&", "(childredatalist", "(systemmenuent", "(var", "///", "0)", "0,", "04", "06", "1.html?t=1\";", "2021", "22:10:57", "=", "==", "===", ">", "[key]", "[required]", "[table(\"bee_system_menu\")]", "api项目数据库访问自行百度,数据库结构参考文档就行，结构都类似。", "asp.net", "bool", "child", "childredatalist)", "childredatalist.count()", "class", "console.log(\"plugin", "core", "e.target.tagnam", "foreach", "function(e){", "get;", "gettreenodelistbynolockeddtoarray(systemmenuentities,", "gettreenodelistbynolockeddtoarray(systemmenuentity[]", "gitbook文件修订时间：", "home", "homeinfo", "href", "icon", "id", "imag", "int", "item", "item);", "item.href,", "item.icon,", "item.id,", "item.title,", "list", "list();", "logo", "logoinfo", "long", "menuinfo", "menusinforesultdto", "menusinforesultdto.menuinfo", "new", "null", "p.pid", "pid", "popup....\");document.onclick", "public", "reserved，pow", "right", "rootnod", "rootnode)", "rootnode.child", "rootnode.child)", "rootnode.child.add(treenode);", "rootnode.id);", "set;", "sort", "static", "statu", "string", "systemmenu", "systemmenu()", "systemmenuent", "systemmenuentities,", "systemmenuentities.count()", "target", "titl", "treenod", "void", "webapi接口示例", "webapi接口示例asp.net", "webapi示例", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "||", "}", "};", "©", "创建asp.net", "创建一个根对象来接受处理好的数据", "名称", "子集", "完整的后端示例地址：https://github.com/chenyi2006520/systemmenu", "序号", "排序", "数据id", "数据对象如下：", "新开tab方式", "是否菜单", "最后将rootnode的child", "权限菜单树", "树结构对象", "父级id", "示例提供来源：a0~海阔天空", "节点名称", "节点地址", "菜单图标", "菜单图标样式", "菜单结果对象", "菜单表", "赋值返回给", "返回给前端就行", "递归处理数据", "递归处理数据库返回的数据方法参考如下，", "链接"], "iframe-v2.html": ["\"\"", "\"\",", "\"_self\",", "\"api/clear.json\",", "\"api/init.json\",", "\"child\":[...]", "\"code\":", "\"fa", "\"homeinfo\":", "\"href\":", "\"icon\":", "\"image\":", "\"images/logo.png\",", "\"img\"", "\"layui", "\"logoinfo\":", "\"menuinfo\":", "\"msg\":", "\"page/welcom", "\"target\":", "\"title\":", "\"其它管理\",", "\"常规管理\",", "\"清除服务端缓存成功\"", "\"组件管理\",", "\"首页\",", "$", "&&", "'#0c0f13',", "'#192027',", "'#1e9fff',", "'#28333e',", "'#565656',", "'#e4e4e4',", "'#ffffff',", "'layer',", "'miniadmin'],", "'rgb(191,", "'rg<PERSON>(107,", "'rgba(160,", "'最终的提交信息'", "()", "(data)", "//", "//logo字体颜色,", "//logo背景颜色,", "//tab选项卡选中颜色,", "//头部右侧下拉字体颜色,", "//头部右侧字体颜色,", "//头部右侧更多下拉列表字体色,", "//头部右侧更多下拉列表选中背景色,", "//头部右侧更多下拉颜色,", "//头部右侧背景色", "//头部右侧选中背景色,", "//头部右侧鼠标选中,", "//头部缩放按钮样式,", "//左侧菜单子菜单背景,", "//左侧菜单字体颜色,", "//左侧菜单更多下拉样式,", "//左侧菜单背景,", "//左侧菜单选中字体颜色,", "//左侧菜单选中背景,", "//监听提交", "0", "0,", "0.7)',", "04", "06", "1,", "1.html?t=1\"", "1.html页面中有", "107,", "160,", "187)',", "187,", "20", "2021", "22:10:57", "2之间", "=", "===", "[", "]", "];", "address", "bgcolorconfig", "bgcolordefault", "bgcolordefault:", "book\",", "bool", "<PERSON><PERSON>l", "clearurl:", "config.js文件，请根据实际情况修改里面扩展的路径。", "console.log(\"plugin", "content", "e.target.tagnam", "fa", "fals", "false,", "false;", "form", "form.on('submit(savebtn)',", "function", "function(e){", "gitbook文件修订时间：", "headerlogobg:", "headerlogocolor:", "headerrightbg:", "headerrightbgthis:", "headerrightchildcolor:", "headerrightcolor:", "headerrightcolorthis:", "headerrightnavmore:", "headerrightnavmorebg:", "headerrightnavmorecolor:", "headerrighttoolcolor:", "homeinfo", "href:\"page/form.html\",", "href：页面链接，data", "iframe版", "iframe窗口动画", "iis环境下请配置支持解析.json格式文件", "index", "iniurl", "iniurl:", "int", "js模块的拆分，代码更加规范化。", "layer", "layer.alert(json.stringify(data.field),", "layer.close(index);", "layui.form,", "layui.j<PERSON>y,", "layui.layer,", "layui.miniadmin;", "layui.minitab;", "layui.use(['form','minitab'],", "layui.use(['jquery',", "leftmenubg:", "leftmen<PERSON>gthis:", "leftmenuchildbg:", "leftmenucolor:", "left<PERSON><PERSON><PERSON><PERSON><PERSON>:", "leftmenunavmore:", "lemon", "loadingtim", "loadingtime:", "logoinfo", "maxtab<PERSON>", "menuchildopen", "menuchildopen:", "menuinfo", "mini\",", "miniadmin", "miniadmin.render(options);", "minitab", "minitab.deletecurrentbyiframe();", "minitab.listen();", "minitab.opennewtabbyiframe({", "module/layuimini/minitheme.js文件内添加", "multimodul", "multimodule:", "null", "o\",", "option", "<PERSON><PERSON>m", "pageanim:", "password.html,us", "popup....\");document.onclick", "reserved，pow", "return", "right", "setting.html页面中都有", "slideshare\",", "string", "tabactivecolor:", "tab选项卡进行重构，视觉和操作体验上更加良好。", "title:", "title:\"按钮示例\",", "title：标题）", "true,", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urlhashlocation:", "v2版本相比于v1，核心代码进行重构，更加更加规范，配置使用起来也更方便。", "v2版本）", "v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中弹出新的tab窗口（标签）在页面中弹出新的tab窗口（js方法）在iframe页面中关闭当前tab窗口后台主题方案配色常见问题备注信息使用说明（ifram", "var", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "});", "},", "};", "©", "个人建议关闭", "个人建议开启", "主题默认配置", "以下参数是miniadmin.render();初始化时进行传入。", "优化主题配色方案", "优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。", "优化手机端初始化时的自适应，不会出现闪动的问题。", "使用说明（ifram", "修改js后刷新页面未生效，请尝试清除浏览器缓存。", "初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api接口返回的参数说明", "初始化加载时间", "初始化接口", "参数", "参数说明（href：页面链接，title：标题）", "参数说明（<PERSON>uimini", "后台主题方案配色", "后台模板初始化", "在iframe页面中关闭当前tab窗口", "在index.html文件内进行初始化", "在页面中弹出新的tab窗口（js方法）", "在页面中弹出新的tab窗口（标签）", "基本资料", "基础参数一览表", "备注", "备注信息", "如需在iframe页面中，请参考下方代码。（备注：minitab.js文件）", "如需在页面中弹出新的tab窗口，请参考下方代码。（备注：需要引入minitab.js文件）", "如需添加更多主题信息，请在js/lay", "实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json", "常见问题", "建议0", "开启后，会显示路由信息，刷新页面后将定位到当前页", "引入lay", "引入miniadmin模块，根据需要传入初始化参数，执行miniadmin.render(options);", "打开新的窗口", "新增tab选项卡的切换与菜单之间的联动功能。", "新增初始化加载层，更好的视觉体验", "新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。", "新增菜单在初始化的时候可以展开子菜单。", "方法。", "是logo信息", "是否开启多模块", "是否打开hash定位", "是否默认展开菜单", "是头部模块和左侧菜单对应的信息", "是首页信息", "更新说明", "最大的tab打开数量", "添加tab或者切换时的过渡动漫", "示例在page/welcom", "示例在user", "示例说明", "类型", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在minitheme.config方法内按相同格式添加。", "缓存清理接口", "缓存清理接口返回的参数说明", "菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。", "说明", "调用方法进行监听：minitab.listen();", "调用方法：minitab.deletecurrentbyiframe();", "返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)", "配置项移出到外部的初始化配置里面。", "重构手机端左侧菜单，弹出菜单时不会挤压内容内面。", "防止打开太多的tab窗口导致页面卡死", "默认值"], "onepage-v2.html": ["\"\"", "\"\",", "\"_self\",", "\"api/clear.json\",", "\"api/init.json\",", "\"child\":[...]", "\"code\":", "\"fa", "\"homeinfo\":", "\"href\":", "\"icon\":", "\"image\":", "\"images/logo.png\",", "\"img\"", "\"layui", "\"logoinfo\":", "\"menuinfo\":", "\"msg\":", "\"page/welcom", "\"target\":", "\"title\":", "\"其它管理\",", "\"常规管理\",", "\"清除服务端缓存成功\"", "\"组件管理\",", "\"首页\",", "$", "&&", "'#0c0c0c',", "'#197971',", "'#1aa094',", "'#23262e',", "'#243346',", "'#2f4056',", "'#3b3f4b',", "'#737373',", "'layer',", "'miniadmin'],", "'最终的提交信息'", "()", "(data)", "*", "*/", "/**", "//", "//监听提交", "0", "0,", "04", "06", "1,", "1.html?t=1\"", "1.html页面中有", "2021", "22:10:57", "2之间", "=", "===", "[", "]", "];", "address", "back", "bgcolorconfig", "bgcolordefault", "bgcolordefault:", "book\",", "bool", "<PERSON><PERSON>l", "clearurl:", "config.js文件，请根据实际情况修改里面扩展的路径。", "console.log(\"plugin", "content", "e.target.tagnam", "fa", "fals", "false,", "false;", "form", "form.on('submit(savebtn)',", "form.render();", "function", "function(e){", "gitbook文件修订时间：", "headerlogo:", "headerright:", "headerrightthis:", "homeinfo", "home首页", "href=：页面链接，data", "iis环境下请配置支持解析.json格式文件", "index", "iniurl", "iniurl:", "int", "js模块的拆分，代码更加规范化。", "layer", "layer.alert(json.stringify(data.field),", "layer.close(index);", "layui.form,", "layui.j<PERSON>y,", "layui.layer,", "layui.miniadmin;", "layui.minipage;", "layui.use(['form','minipage'],", "layui.use(['jquery',", "lemon", "loadingtim", "loadingtime:", "logoinfo", "menuchildopen", "menuchildopen:", "menuinfo", "menuleft:", "menulefthover:", "menuleftthis:", "mini\",", "miniadmin", "miniadmin.render(options);", "minipag", "minipage.hashhome();", "module/layuimini/minitheme.js文件内添加", "multimodul", "multimodule:", "null", "o\",", "option", "password.html,us", "popup....\");document.onclick", "renderpagevers", "reserved，pow", "return", "right", "setting.html页面中都有", "slideshare\",", "string", "tabactive:", "title:", "title：标题）", "true,", "urlhashlocation:", "v2版本相比于v1，核心代码进行重构，更加更加规范，配置使用起来也更方便。", "v2版本）", "v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中打开新的页面在内容页面中返回主页后台主题方案配色常见问题备注信息使用说明（单页面", "var", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "});", "},", "};", "©", "个人建议关闭", "个人建议开启", "主题默认配置", "以下参数是miniadmin.render();初始化时进行传入。", "优化主题配色方案", "优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。", "优化手机端初始化时的自适应，不会出现闪动的问题。", "优化返回主页按钮以及页面导航栏的实现", "使用说明（单页面", "修改js后刷新页面未生效，请尝试清除浏览器缓存。", "初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api接口返回的参数说明", "初始化加载时间", "初始化接口", "初始化表单，要加上，不然刷新部分组件可能会不加载", "初始化页面是否加版本号", "单页封装进行重构，视觉和操作体验上更加良好。", "单页版", "参数", "参数说明（<PERSON>uimini", "后台主题方案配色", "后台模板初始化", "在index.html文件内进行初始化", "在内容页面中返回主页", "在页面中打开新的页面", "在页面中打开新的页面，请参考下方代码。（备注：需要引入minipage.js文件）", "基本资料", "基础参数一览表", "备注", "备注信息", "如需添加更多主题信息，请在js/lay", "实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json", "常见问题", "建议0", "开启后，页面不会有缓存问题", "引入lay", "引入miniadmin模块，根据需要传入初始化参数，执行miniadmin.render(options);", "新增初始化加载层，更好的视觉体验", "新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。", "新增初始化页面是否加版本号", "新增菜单在初始化的时候可以展开子菜单。", "方法。", "方法一：添加class样式layuimini", "方法二：minipage.hashhome();，示例在us", "是logo信息", "是否开启多模块", "是否打开hash定位", "是否默认展开菜单", "是头部模块和左侧菜单对应的信息", "是首页信息", "更新说明", "示例在page/welcom", "示例说明", "类型", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在minitheme.bgcolorconfig方法内按相同格式添加。", "缓存清理接口", "缓存清理接口返回的参数说明", "菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。", "说明", "调用方法进行监听：minipage.listen();(备注：框架初始化时已经进行监听，一般情况下不需要再次操作)", "返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)", "配置项移出到外部的初始化配置里面。", "重构手机端左侧菜单，弹出菜单时不会挤压内容内面。", "默认值"], "iframe.html": ["\"\"", "\"api/clear.json\"", "\"child\":", "\"clearinfo\":", "\"clearurl\":", "\"code\":", "\"currency\":", "\"fa", "\"homeinfo\":", "\"href\":", "\"icon\":", "\"image\":", "\"images/logo.png\",", "\"img\"", "\"js/\",", "\"<PERSON>ui<PERSON>\"", "\"layuimini\",", "\"logoinfo\":", "\"menuinfo\":", "\"msg\":", "\"other\":", "\"page/welcom", "\"title\":", "\"其它管理\",", "\"常规管理\",", "\"清除服务端缓存成功\"", "\"首页\",", "$", "&&", "'#0c0c0c',", "'#197971',", "'#1aa094',", "'#23262e',", "'#243346',", "'#2f4056',", "'#3b3f4b',", "'layer',", "'layuimini'],", "'最终的提交信息'", "()", "(data)", ".......", "//", "//监听提交", "0\"", "0,", "04", "06", "1,", "2.html?mpi=m", "2021", "22:10:57", "=", "===", "[", "]", "],", "];", "address", "base:", "bgcolorconfig", "bgcolordefault:", "bgcolordefault：系统默认皮肤，从0开始。", "book\",", "checkurldefault:", "checkurldefault：是否判断url有效，默认开启。", "clearinfo是服务端清理缓存信息(clearinfo.clearurl：服务端清理缓存接口地址，为空则不请求;)", "config", "config.js文件，请根据实际情况修改里面扩展的路径。", "console.log(\"plugin", "e.target.tagnam", "element", "fa", "false;", "form", "form.on('submit(savebtn)',", "function", "function(e){", "gitbook文件修订时间：", "headerlogo:", "headerright:", "headerrightthis:", "home\",", "homeinfo", "icon：图标）", "id必须唯一，例如", "ifram", "iframe版", "iis环境下请配置支持解析.json格式文件", "index", "layer", "layer.alert(json.stringify(data.field),", "layer.close(index);", "layui.config({", "layui.element,", "layui.form,", "layui.j<PERSON>y,", "layui.layer,", "layui.layer;", "layui.layuimini;", "layui.use(['element',", "layui.use(['form','layuimini'],", "<PERSON><PERSON><PERSON>", "layuimini.closecurrenttab();", "layuimini.init('api/init.json');", "layuimini.init();", "<PERSON><PERSON><PERSON>:", "logoinfo", "menuinfo", "menuinfo.currency、menuinfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。", "menuleft:", "menulefthover:", "menuleftthis:", "menumodul", "p", "password.html,us", "popup....\");document.onclick", "reserved，pow", "return", "right", "setting.html页面中都有", "slideshare\",", "tab：页面链接，data", "title:", "title：标题，data", "true", "true,", "urlhashlocation:", "urlhashlocation：是否开启url地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页", "urlsuffixdefault:", "urlsuffixdefault：是否开启url后缀，默认开启。", "url后缀", "url地址hash定位", "v1版本）", "v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中弹出新的tab窗口在iframe页面中关闭当前tab窗口后台主题方案配色常见问题备注信息使用说明（ifram", "var", "version:", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "}).extend({", "}).use(['layuimini'],", "});", "},", "};", "©", "使用说明（ifram", "修改js后刷新页面未生效，请尝试清除浏览器缓存。", "初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api地址返回的参数说明", "参数说明（data", "后台主题方案配色", "后台模板初始化", "在iframe页面中关闭当前tab窗口", "在index.html文件内进行初始化", "在页面中弹出新的tab窗口", "基本资料", "备注信息", "如需在iframe页面中，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在页面中弹出新的tab窗口，请参考下方代码。（备注：需要引入layuimini.js文件）", "常见问题", "引入lay", "方法内的参数请填写动态api地址。（实际应用中，请以后端api接口方式去实现）", "是logo信息", "是否判断url有效", "是头部模块和左侧菜单对应的信息", "是首页信息", "示例在user", "示例说明", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgcolorconfig方法内按相同格式添加。", "菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。", "调用方法：layuimini.closecurrenttab();", "返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)", "默认皮肤（0开始）", "默认配置在layuimini.config方法内，请自行修改", "默认配置说明"], "onepage.html": ["\"\"", "\"api/clear.json\"", "\"child\":", "\"clearinfo\":", "\"clearurl\":", "\"code\":", "\"currency\":", "\"fa", "\"homeinfo\":", "\"href\":", "\"icon\":", "\"image\":", "\"images/logo.png\",", "\"img\"", "\"layuimini\",", "\"logoinfo\":", "\"menuinfo\":", "\"msg\":", "\"other\":", "\"page/welcom", "\"title\":", "\"其它管理\",", "\"常规管理\",", "\"清除服务端缓存成功\"", "\"首页\",", "$", "&&", "'#0c0c0c',", "'#197971',", "'#1aa094',", "'#23262e',", "'#243346',", "'#2f4056',", "'#3b3f4b',", "'layer',", "'layuimini'],", "'最终的提交信息'", "()", "(data)", "*", "*/", ".......", "/**", "//", "//监听提交", "0", "0\"", "04", "06", "1,", "1.html');", "2.html?mpi=m", "2021", "22:10:57", "=", "===", "[", "]", "],", "];", "address", "bgcolorconfig", "bgcolordefault:", "bgcolordefault：系统默认皮肤，从0开始。", "book\",", "clearinfo是服务端清理缓存信息(clearinfo.clearurl：服务端清理缓存接口地址，为空则不请求;)", "config", "config.js文件，请根据实际情况修改里面扩展的路径。", "console.log(\"plugin", "e.target.tagnam", "element", "fa", "false;", "form", "form.on('submit(savebtn)',", "form.render();", "form表单刷新，部分组件不显示的情况，请在js上加上form.render();", "function", "function(e){", "gitbook文件修订时间：", "headerlogo:", "headerright:", "headerrightthis:", "home\",", "homeinfo", "icon：图标）", "id必须唯一，例如", "ifram", "iis环境下请配置支持解析.json格式文件", "index", "layer", "layer.alert(json.stringify(data.field),", "layer.close(index);", "layui.element,", "layui.form,", "layui.j<PERSON>y,", "layui.layer,", "layui.layer;", "layui.layuimini;", "layui.use(['element',", "layui.use(['form','layuimini'],", "<PERSON><PERSON><PERSON>", "layuimini.hash('page/welcom", "layuimini.init('api/init.json');", "layuimini.init();", "layuimini.refresh();", "logoinfo", "menuinfo", "menuinfo.currency、menuinfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。", "menuleft:", "menulefthover:", "menuleftthis:", "menumodul", "p", "password.html页面中", "popup....\");document.onclick", "reserved，pow", "return", "right", "setting.html页面中", "slideshare\",", "tab：页面链接，data", "title:", "title：标题，data", "true,", "urlhashlocation:", "urlhashlocation：是否开启url地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页", "urlsuffixdefault:", "urlsuffixdefault：是否开启url后缀，默认开启。", "url后缀", "url地址hash定位", "v1版本）", "v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中打开新页面在js中跳转页面在js中局部刷新页面后台主题方案配色常见问题备注信息使用说明（单页面", "var", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "}", "});", "},", "};", "©", "使用说明（单页面", "修改js后刷新页面未生效，请尝试清除浏览器缓存。", "初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api地址返回的参数说明", "初始化表单，要加上，不然刷新部分组件可能会不加载", "单页版", "参数说明（data", "后台主题方案配色", "后台模板初始化", "在index.html文件内进行初始化", "在js中局部刷新页面", "在js中跳转页面", "在页面中打开新页面", "基本资料", "备注信息", "如需在js局部刷新页面，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在js跳转页面，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在页面中弹出新的tab窗口，请参考下方代码。", "常见问题", "引入lay", "方法内的参数请填写动态api地址。（实际应用中，请以后端api接口方式去实现）", "是logo信息", "是头部模块和左侧菜单对应的信息", "是首页信息", "示例在user", "示例说明", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgcolorconfig方法内按相同格式添加。", "菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。", "调用方法：layuimini.hash(href);", "调用方法：layuimini.refresh();", "返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)", "默认皮肤（0开始）", "默认配置在layuimini.config方法内，请自行修改", "默认配置说明"]}, "length": 11}, "tokenStore": {"root": {"0": {"4": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "6": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "l": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}, ".": {"7": {"docs": {}, ")": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}}, "docs": {}}, "\"": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}, "1": {"0": {"0": {"0": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}, "docs": {}}, "7": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.00904977375565611}}}}, "docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "2": {"3": {"4": {"5": {"6": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "6": {"0": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}}}}, "docs": {}}, "8": {"0": {"0": {"0": {"0": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "7": {"docs": {}, ")": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}, "docs": {}}, "docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "?": {"docs": {}, "t": {"docs": {}, "=": {"1": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}, "docs": {}}}}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "有": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}, "'": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "控": {"docs": {}, "制": {"docs": {}, "器": {"docs": {}, "路": {"docs": {}, "由": {"docs": {}, ",": {"docs": {}, "自": {"docs": {}, "行": {"docs": {}, "定": {"docs": {}, "义": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}, "永": {"docs": {}, "不": {"docs": {}, "超": {"docs": {}, "时": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}, "2": {"0": {"2": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "1": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "docs": {}}, "docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "2": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}, ":": {"1": {"0": {"docs": {}, ":": {"5": {"7": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "docs": {}}, "docs": {}}}, "docs": {}}, "docs": {}}}, "5": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "之": {"docs": {}, "间": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "?": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "=": {"docs": {}, "m": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "3": {"0": {"0": {"0": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "5": {"4": {"2": {"1": {"7": {"5": {"7": {"6": {"3": {"0": {"1": {"2": {"1": {"6": {"3": {"6": {"0": {"0": {"6": {"docs": {}, "l": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "6": {"0": {"0": {"0": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "8": {"0": {"8": {"0": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}, "docs": {}}, "docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, "&": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "z": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "=": {"docs": {}, "g": {"docs": {}, "m": {"docs": {}, "t": {"docs": {}, "%": {"2": {"docs": {}, "b": {"8": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}, "docs": {}, "\"": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "g": {"docs": {}, "\"": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}}}}}}, "d": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "s": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "x": {"docs": {}, "i": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, "/": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "\"": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}, "n": {"docs": {}, "y": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.011312217194570135}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.014124293785310734}, "iframe.html": {"ref": "iframe.html", "tf": 0.015267175572519083}, "onepage.html": {"ref": "onepage.html", "tf": 0.013468013468013467}}}}}}}}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "m": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}}}}, "首": {"docs": {}, "页": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, ",": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "_": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "f": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}}}, "c": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "[": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, "]": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}}}}}}}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "y": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.011312217194570135}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.014124293785310734}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "o": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "m": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "d": {"docs": {}, "s": {"docs": {}, "d": {"docs": {}, "s": {"docs": {}, "d": {"docs": {}, "s": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "\"": {"docs": {}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "{": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}, "\"": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "根": {"docs": {}, "目": {"docs": {}, "录": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}}}}}}, "f": {"docs": {}, "a": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}, "s": {"docs": {}, "g": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}, "其": {"docs": {}, "它": {"docs": {}, "管": {"docs": {}, "理": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "常": {"docs": {}, "规": {"docs": {}, "管": {"docs": {}, "理": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "清": {"docs": {}, "除": {"docs": {}, "服": {"docs": {}, "务": {"docs": {}, "端": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "成": {"docs": {}, "功": {"docs": {}, "\"": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}, "组": {"docs": {}, "件": {"docs": {}, "管": {"docs": {}, "理": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}, "o": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "\"": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "&": {"docs": {}, "&": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "{": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}, "=": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.088}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.09016393442622951}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.04926108374384237}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.0425531914893617}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.038461538461538464}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.031073446327683617}, "iframe.html": {"ref": "iframe.html", "tf": 0.03816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.04713804713804714}}, "=": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, "=": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}, ">": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.048}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.04918032786885246}}}, "\"": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "_": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "表": {"docs": {}, "\"": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "a": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "图": {"docs": {}, "标": {"docs": {}, "库": {"docs": {}, "的": {"docs": {}, "完": {"docs": {}, "美": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}, "选": {"docs": {}, "择": {"docs": {}, "插": {"docs": {}, "件": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}, "u": {"docs": {}, "t": {"docs": {}, "o": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "_": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "r": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "=": {"2": {"5": {"0": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}}}}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}, "p": {"docs": {}, "p": {"docs": {}, "/": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "/": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "/": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}, "l": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "i": {"docs": {}, "项": {"docs": {}, "目": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {}, "访": {"docs": {}, "问": {"docs": {}, "自": {"docs": {}, "行": {"docs": {}, "百": {"docs": {}, "度": {"docs": {}, ",": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "文": {"docs": {}, "档": {"docs": {}, "就": {"docs": {}, "行": {"docs": {}, "，": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "都": {"docs": {}, "类": {"docs": {}, "似": {"docs": {}, "。": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "/": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "'": {"docs": {}, "}": {"docs": {}, "}": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "d": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "s": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "r": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "<": {"docs": {}, ">": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}, "s": {"docs": {}, "p": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 3.3356973995271866}}}}}}}}}, "b": {"docs": {"./": {"ref": "./", "tf": 0.06611570247933884}}, "u": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "$": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "o": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.00904977375565611}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}, "e": {"docs": {}, "a": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "k": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}, "g": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "：": {"docs": {}, "系": {"docs": {}, "统": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "皮": {"docs": {}, "肤": {"docs": {}, "，": {"docs": {}, "从": {"0": {"docs": {}, "开": {"docs": {}, "始": {"docs": {}, "。": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}, "a": {"docs": {}, "c": {"docs": {}, "k": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "s": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}, "c": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {"./": {"ref": "./", "tf": 0.06611570247933884}}}}}, "a": {"docs": {}, "s": {"docs": {}, "s": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.009852216748768473}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.01182033096926714}}, "p": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "/": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "是": {"docs": {}, "服": {"docs": {}, "务": {"docs": {}, "端": {"docs": {}, "清": {"docs": {}, "理": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "(": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "：": {"docs": {}, "服": {"docs": {}, "务": {"docs": {}, "端": {"docs": {}, "清": {"docs": {}, "理": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "，": {"docs": {}, "为": {"docs": {}, "空": {"docs": {}, "则": {"docs": {}, "不": {"docs": {}, "请": {"docs": {}, "求": {"docs": {}, ";": {"docs": {}, ")": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "p": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "n": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "n": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "根": {"docs": {}, "据": {"docs": {}, "实": {"docs": {}, "际": {"docs": {}, "情": {"docs": {}, "况": {"docs": {}, "修": {"docs": {}, "改": {"docs": {}, "里": {"docs": {}, "面": {"docs": {}, "扩": {"docs": {}, "展": {"docs": {}, "的": {"docs": {}, "路": {"docs": {}, "径": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.0967741935483871}}, "=": {"docs": {}, "'": {"docs": {}, "系": {"docs": {}, "统": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "表": {"docs": {}, "'": {"docs": {}, ";": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, ".": {"docs": {}, "m": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "q": {"docs": {}, "l": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "d": {"docs": {}, "b": {"docs": {}, "c": {"docs": {}, ".": {"docs": {}, "d": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "v": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}, "z": {"docs": {}, "a": {"docs": {}, "x": {"docs": {}, "x": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "k": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "k": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 3.340425531914893}}}}}, "h": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "=": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "f": {"8": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}, "docs": {}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.003940886699507389}}}, "r": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "u": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "c": {"docs": {}, "k": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}, "：": {"docs": {}, "是": {"docs": {}, "否": {"docs": {}, "判": {"docs": {}, "断": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "有": {"docs": {}, "效": {"docs": {}, "，": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "开": {"docs": {}, "启": {"docs": {}, "。": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, ".": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "[": {"docs": {}, "\"": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {}, "]": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "a": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ".": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}, "n": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "=": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "b": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}}}, "c": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, "层": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "x": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "l": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}, "l": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "n": {"docs": {}, "a": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "d": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "b": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "v": {"docs": {}, "o": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}, "u": {"docs": {}, "n": {"docs": {}, "c": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.017241379310344827}}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.013574660633484163}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.011299435028248588}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.02356902356902357}}, "(": {"docs": {}, "e": {"docs": {}, ")": {"docs": {}, "{": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "o": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "c": {"docs": {}, "h": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}}}}}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, ".": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "b": {"docs": {}, "t": {"docs": {}, "n": {"docs": {}, ")": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}, "表": {"docs": {}, "单": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "，": {"docs": {}, "部": {"docs": {}, "分": {"docs": {}, "组": {"docs": {}, "件": {"docs": {}, "不": {"docs": {}, "显": {"docs": {}, "示": {"docs": {}, "的": {"docs": {}, "情": {"docs": {}, "况": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "在": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "上": {"docs": {}, "加": {"docs": {}, "上": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, ".": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, ":": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "y": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "/": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, ".": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}, "a": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}, "l": {"docs": {}, "s": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.00904977375565611}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}, "e": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}, "v": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "b": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "k": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "修": {"docs": {}, "订": {"docs": {}, "时": {"docs": {}, "间": {"docs": {}, "：": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "e": {"docs": {}, "e": {"docs": {}, "下": {"docs": {}, "载": {"docs": {}, "命": {"docs": {}, "令": {"docs": {}, "：": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}}}}}}}}}, "仓": {"docs": {}, "库": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}, "v": {"2": {"docs": {"./": {"ref": "./", "tf": 0.01652892561983471}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "发": {"docs": {}, "版": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "s": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, "下": {"docs": {}, "载": {"docs": {}, "命": {"docs": {}, "令": {"docs": {}, "：": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}}}}}}}}}, "仓": {"docs": {}, "库": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}, "v": {"2": {"docs": {"./": {"ref": "./", "tf": 0.01652892561983471}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "发": {"docs": {}, "版": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "s": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "{": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "a": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "c": {"docs": {}, "k": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "d": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "y": {"docs": {}, "[": {"docs": {}, "]": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.05673758865248227}}}}, "n": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "（": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "）": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "（": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "）": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "示": {"docs": {}, "例": {"docs": {}, "(": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 10}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.0070921985815602835}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, ".": {"docs": {}, "p": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "\"": {"docs": {}, "/": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "\"": {"docs": {}, "首": {"docs": {}, "页": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}, "首": {"docs": {}, "页": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "\"": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.014184397163120567}}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "\"": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.007881773399014778}}}, "：": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "链": {"docs": {}, "接": {"docs": {}, "，": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}, "=": {"docs": {}, "：": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "链": {"docs": {}, "接": {"docs": {}, "，": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "<": {"docs": {}, ">": {"docs": {}, "(": {"1": {"6": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}, "docs": {}}, "docs": {}}}}}}}}}}, "i": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "k": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "e": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "b": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, "r": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "b": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}, "n": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "b": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}, "t": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"./": {"ref": "./", "tf": 0.01652892561983471}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "e": {"docs": {}, "版": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 10}, "iframe.html": {"ref": "iframe.html", "tf": 10}}}, "窗": {"docs": {}, "口": {"docs": {}, "动": {"docs": {}, "画": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}}}}}}}}}}}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "p": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}, "l": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0049261083743842365}}}}}}}}}}, "n": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.017241379310344827}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "(": {"1": {"1": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}}}, "docs": {}}, "docs": {}}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, "e": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}, "r": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "和": {"docs": {}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "注": {"docs": {}, "解": {"docs": {}, ",": {"docs": {}, "解": {"docs": {}, "决": {"docs": {}, "新": {"docs": {}, "增": {"docs": {}, "和": {"docs": {}, "更": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "时": {"docs": {}, "候": {"docs": {}, "没": {"docs": {}, "有": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "值": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.009456264775413711}}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.006896551724137931}}}, "：": {"docs": {}, "图": {"docs": {}, "标": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.01182033096926714}}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.008866995073891626}}}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "必": {"docs": {}, "须": {"docs": {}, "唯": {"docs": {}, "一": {"docs": {}, "，": {"docs": {}, "例": {"docs": {}, "如": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, ")": {"docs": {}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, ".": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "d": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}, "i": {"docs": {}, "s": {"docs": {}, "环": {"docs": {}, "境": {"docs": {}, "下": {"docs": {}, "请": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "解": {"docs": {}, "析": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "文": {"docs": {}, "件": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "后": {"docs": {}, "台": {"docs": {}, "模": {"docs": {}, "板": {"docs": {}, "项": {"docs": {}, "目": {"docs": {}, "介": {"docs": {}, "绍": {"docs": {}, "主": {"docs": {}, "要": {"docs": {}, "特": {"docs": {}, "性": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "仓": {"docs": {}, "库": {"docs": {}, "(": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}, ".": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "'": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}, ")": {"docs": {}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}, ".": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, "j": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.00904977375565611}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, ";": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}, "u": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "[": {"docs": {}, "'": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}}}}}}}}, "j": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "(": {"docs": {}, "{": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "e": {"docs": {}, "r": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.00904977375565611}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}, ".": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, ".": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, ")": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "c": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "(": {"6": {"docs": {}, ".": {"2": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, ")": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "(": {"6": {"docs": {}, ".": {"2": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}, "docs": {}}}, "docs": {}}}}}}}}}}}}}}}}}, "docs": {}}}, "docs": {}}}}}}}}}}}}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}, "g": {"docs": {}, "a": {"docs": {}, "c": {"docs": {}, "y": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"5": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "docs": {}}}}}}}}}, "v": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "f": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "b": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}, "n": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}, "m": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.0070921985815602835}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, ".": {"docs": {}, "p": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "\"": {"docs": {}, "/": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "b": {"docs": {}, "a": {"docs": {}, "c": {"docs": {}, "k": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "p": {"docs": {}, "g": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "静": {"docs": {}, "态": {"docs": {}, "资": {"docs": {}, "源": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "路": {"docs": {}, "径": {"docs": {}, ",": {"docs": {}, "可": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "的": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {}, "\"": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "管": {"docs": {}, "理": {"docs": {}, "系": {"docs": {}, "统": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}, "g": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.016748768472906402}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.009456264775413711}}}}, "a": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "e": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "i": {"docs": {}, "f": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "s": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.009852216748768473}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}, "m": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"./": {"ref": "./", "tf": 0.01652892561983471}}}}}}, "p": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.005911330049261084}}, ".": {"docs": {}, "p": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "x": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "i": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "m": {"docs": {}, "u": {"docs": {}, "m": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ".": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, "o": {"docs": {}, "p": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}, ".": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}, "o": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "w": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "{": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "e": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}, ".": {"docs": {}, "b": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"0": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, "docs": {}}}}}}}}}}}}}}}}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, ".": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "v": {"docs": {}, "o": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "y": {"docs": {}, "、": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "对": {"docs": {}, "应": {"docs": {}, "的": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "y": {"docs": {}, "和": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "就": {"docs": {}, "是": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "，": {"docs": {}, "他": {"docs": {}, "们": {"docs": {}, "的": {"docs": {}, "值": {"docs": {}, "必": {"docs": {}, "须": {"docs": {}, "唯": {"docs": {}, "一": {"docs": {}, "，": {"docs": {}, "否": {"docs": {}, "则": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "切": {"docs": {}, "换": {"docs": {}, "会": {"docs": {}, "有": {"docs": {}, "冲": {"docs": {}, "突": {"docs": {}, "。": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "e": {"docs": {}, "f": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}, "h": {"docs": {}, "o": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0049261083743842365}}}}}, "v": {"docs": {}, "o": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0049261083743842365}}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, ".": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}, "d": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {}, "d": {"docs": {}, "t": {"docs": {}, "o": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, ".": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "o": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "n": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "u": {"docs": {}, "l": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "s": {"docs": {}, "/": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "u": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "内": {"docs": {}, "添": {"docs": {}, "加": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "v": {"docs": {}, "c": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "y": {"docs": {}, "s": {"docs": {}, "q": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "u": {"docs": {}, "l": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "e": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}}}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.049586776859504134}}}}}}}, ".": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "m": {"docs": {}, ".": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "\"": {"docs": {}, ",": {"1": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "r": {"docs": {}, "m": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "w": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "g": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "b": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "d": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "m": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "q": {"docs": {}, "l": {"5": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "p": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}, "p": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "o": {"docs": {}, "p": {"docs": {}, "u": {"docs": {}, "p": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {}, "d": {"docs": {}, "o": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "k": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "r": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "r": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "v": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}, "init/java.html": {"ref": "init/java.html", "tf": 0.04334975369458128}}}}}}, "e": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "x": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "h": {"docs": {}, "p": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 10}}}}}}}}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "k": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {}, ")": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 10}}}}}}}}}}}}}}}, "y": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "a": {"docs": {}, "c": {"docs": {}, "k": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "w": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "s": {"docs": {}, "s": {"docs": {}, "w": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, ",": {"docs": {}, "u": {"docs": {}, "s": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}, "t": {"docs": {}, "h": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, "s": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "g": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.006896551724137931}}}}}, "e": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "_": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "u": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.05517241379310345}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.07092198581560284}}}}}}}, ".": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "，": {"docs": {}, "p": {"docs": {}, "o": {"docs": {}, "w": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}, "p": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}, "o": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "p": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ".": {"docs": {}, "y": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "t": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "n": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.024}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.02459016393442623}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}, "init/java.html": {"ref": "init/java.html", "tf": 0.024630541871921183}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ".": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}}, "p": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, "/": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, "层": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "s": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}, "i": {"docs": {}, "g": {"docs": {}, "h": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, "o": {"docs": {}, "w": {"docs": {}, "_": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "=": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "c": {"docs": {}, "t": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "e": {"docs": {}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ".": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ".": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}, "a": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "t": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}, "/": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}, "e": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}, "a": {"docs": {}, "c": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}, ":": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}, "选": {"docs": {}, "项": {"docs": {}, "卡": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "重": {"docs": {}, "构": {"docs": {}, "，": {"docs": {}, "视": {"docs": {}, "觉": {"docs": {}, "和": {"docs": {}, "操": {"docs": {}, "作": {"docs": {}, "体": {"docs": {}, "验": {"docs": {}, "上": {"docs": {}, "更": {"docs": {}, "加": {"docs": {}, "良": {"docs": {}, "好": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}}, "：": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "链": {"docs": {}, "接": {"docs": {}, "，": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.006896551724137931}}}}}}}}, "i": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "p": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}}}}}}, ".": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "o": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}}}, "n": {"docs": {}, "y": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "(": {"1": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}, "docs": {}}}}}}}, "t": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.014184397163120567}}, "e": {"docs": {}, ":": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "\"": {"docs": {}, "按": {"docs": {}, "钮": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.007881773399014778}}}, "：": {"docs": {}, "标": {"docs": {}, "题": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}, "，": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "o": {"docs": {}, "d": {"docs": {}, "o": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, "m": {"docs": {}, "c": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "h": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "k": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "(": {"6": {"docs": {}, ".": {"0": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, ")": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "k": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "(": {"6": {"docs": {}, ".": {"0": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, ")": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}, "docs": {}}}, "docs": {}}}}}}}}}}}}}}}}}}, "docs": {}}}, "docs": {}}}}}}}}}}}, "s": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "s": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "s": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "y": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "f": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "u": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ".": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}, "u": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "/": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "u": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0049261083743842365}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.011312217194570135}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.011299435028248588}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}, "y": {"docs": {}, "p": {"docs": {}, "e": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "定": {"docs": {}, "位": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "，": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "清": {"docs": {}, "楚": {"docs": {}, "看": {"docs": {}, "到": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "的": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "c": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "：": {"docs": {}, "是": {"docs": {}, "否": {"docs": {}, "开": {"docs": {}, "启": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "定": {"docs": {}, "位": {"docs": {}, "，": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "开": {"docs": {}, "启": {"docs": {}, "。": {"docs": {}, "关": {"docs": {}, "闭": {"docs": {}, "后": {"docs": {}, "，": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "后": {"docs": {}, "将": {"docs": {}, "定": {"docs": {}, "位": {"docs": {}, "不": {"docs": {}, "到": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "页": {"docs": {}, "，": {"docs": {}, "只": {"docs": {}, "显": {"docs": {}, "示": {"docs": {}, "主": {"docs": {}, "页": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "u": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "x": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "：": {"docs": {}, "是": {"docs": {}, "否": {"docs": {}, "开": {"docs": {}, "启": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "后": {"docs": {}, "缀": {"docs": {}, "，": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "开": {"docs": {}, "启": {"docs": {}, "。": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "后": {"docs": {}, "缀": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "n": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "n": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}}}}}}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "a": {"docs": {}, "t": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "t": {"docs": {}, "f": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}, "v": {"1": {"docs": {}, "版": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}, "本": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "默": {"docs": {}, "认": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "模": {"docs": {}, "板": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "在": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "在": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "关": {"docs": {}, "闭": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "常": {"docs": {}, "见": {"docs": {}, "问": {"docs": {}, "题": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "打": {"docs": {}, "开": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "在": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "中": {"docs": {}, "跳": {"docs": {}, "转": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "在": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "中": {"docs": {}, "局": {"docs": {}, "部": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "常": {"docs": {}, "见": {"docs": {}, "问": {"docs": {}, "题": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "单": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "2": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}, "版": {"docs": {"./": {"ref": "./", "tf": 0.03305785123966942}}, "本": {"docs": {}, "相": {"docs": {}, "比": {"docs": {}, "于": {"docs": {}, "v": {"1": {"docs": {}, "，": {"docs": {}, "核": {"docs": {}, "心": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "重": {"docs": {}, "构": {"docs": {}, "，": {"docs": {}, "更": {"docs": {}, "加": {"docs": {}, "更": {"docs": {}, "加": {"docs": {}, "规": {"docs": {}, "范": {"docs": {}, "，": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "起": {"docs": {}, "来": {"docs": {}, "也": {"docs": {}, "更": {"docs": {}, "方": {"docs": {}, "便": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "docs": {}}}}}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "更": {"docs": {}, "新": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "基": {"docs": {}, "础": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "一": {"docs": {}, "览": {"docs": {}, "表": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "模": {"docs": {}, "板": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "清": {"docs": {}, "理": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "在": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "（": {"docs": {}, "标": {"docs": {}, "签": {"docs": {}, "）": {"docs": {}, "在": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "（": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "）": {"docs": {}, "在": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "关": {"docs": {}, "闭": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "常": {"docs": {}, "见": {"docs": {}, "问": {"docs": {}, "题": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "打": {"docs": {}, "开": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "在": {"docs": {}, "内": {"docs": {}, "容": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "主": {"docs": {}, "页": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "常": {"docs": {}, "见": {"docs": {}, "问": {"docs": {}, "题": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "单": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "a": {"docs": {}, "r": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.01809954751131222}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.01694915254237288}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.02356902356902357}}, "c": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "(": {"1": {"0": {"0": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}}}, "docs": {}}, "docs": {}}, "2": {"0": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}, "5": {"5": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}, ".": {"docs": {}, "b": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "v": {"docs": {}, ".": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "d": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}, "o": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.020689655172413793}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}, "e": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ":": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}, "w": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "o": {"docs": {}, "w": {"docs": {}, ".": {"docs": {}, "o": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, ",": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, ")": {"docs": {}, "}": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "g": {"docs": {}, "{": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, ":": {"docs": {}, "p": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "}": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "b": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "示": {"docs": {}, "例": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "a": {"docs": {}, "s": {"docs": {}, "p": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}, "示": {"docs": {}, "例": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 3.333333333333333}}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "z": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "o": {"docs": {}, "f": {"docs": {}, "a": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "©": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}, "init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, "一": {"docs": {}, "个": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "几": {"docs": {}, "行": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "而": {"docs": {}, "已": {"docs": {}, "直": {"docs": {}, "接": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "整": {"docs": {}, "个": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "，": {"docs": {}, "无": {"docs": {}, "需": {"docs": {}, "复": {"docs": {}, "杂": {"docs": {}, "操": {"docs": {}, "作": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "下": {"docs": {}, "载": {"docs": {}, "方": {"docs": {}, "式": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "主": {"docs": {}, "要": {"docs": {}, "特": {"docs": {}, "性": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}, "题": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "配": {"docs": {}, "置": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}}, "代": {"docs": {}, "码": {"docs": {}, "仓": {"docs": {}, "库": {"docs": {}, "(": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "会": {"docs": {}, "保": {"docs": {}, "留": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "的": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "，": {"docs": {}, "并": {"docs": {}, "且": {"docs": {}, "会": {"docs": {}, "定": {"docs": {}, "位": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "对": {"docs": {}, "应": {"docs": {}, "左": {"docs": {}, "侧": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "栏": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "加": {"docs": {}, "群": {"docs": {}, "请": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "来": {"docs": {}, "源": {"docs": {}, "：": {"docs": {}, "如": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "、": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, "、": {"docs": {}, "官": {"docs": {}, "网": {"docs": {}, "等": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "单": {"docs": {}, "页": {"docs": {}, "版": {"docs": {"./": {"ref": "./", "tf": 0.01652892561983471}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 10}, "onepage.html": {"ref": "onepage.html", "tf": 10}}, ")": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}, "下": {"docs": {}, "载": {"docs": {}, "方": {"docs": {}, "式": {"docs": {}, "效": {"docs": {}, "果": {"docs": {}, "预": {"docs": {}, "览": {"docs": {}, "捐": {"docs": {}, "赠": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "模": {"docs": {}, "板": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "封": {"docs": {}, "装": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "重": {"docs": {}, "构": {"docs": {}, "，": {"docs": {}, "视": {"docs": {}, "觉": {"docs": {}, "和": {"docs": {}, "操": {"docs": {}, "作": {"docs": {}, "体": {"docs": {}, "验": {"docs": {}, "上": {"docs": {}, "更": {"docs": {}, "加": {"docs": {}, "良": {"docs": {}, "好": {"docs": {}, "。": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}, "发": {"docs": {}, "行": {"docs": {}, "版": {"docs": {}, "地": {"docs": {}, "址": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}, "在": {"docs": {}, "线": {"docs": {}, "预": {"docs": {}, "览": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"9": {"9": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "n": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "v": {"1": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}, "2": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}, "docs": {}}}}}}}}}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "v": {"1": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}, "2": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}}}}, "docs": {}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "关": {"docs": {}, "闭": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "内": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}, "（": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}, "标": {"docs": {}, "签": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}, "打": {"docs": {}, "开": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "，": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "下": {"docs": {}, "方": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "。": {"docs": {}, "（": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "引": {"docs": {}, "入": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "内": {"docs": {}, "容": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "主": {"docs": {}, "页": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "中": {"docs": {}, "局": {"docs": {}, "部": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "跳": {"docs": {}, "转": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "多": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "版": {"docs": {}, ")": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}, "代": {"docs": {}, "码": {"docs": {}, "仓": {"docs": {}, "库": {"docs": {}, "(": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}, "失": {"docs": {}, "效": {"docs": {}, "以": {"docs": {}, "及": {"docs": {}, "报": {"docs": {}, "错": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "无": {"docs": {}, "法": {"docs": {}, "直": {"docs": {}, "接": {"docs": {}, "打": {"docs": {}, "开": {"docs": {}, "，": {"docs": {}, "并": {"docs": {}, "给": {"docs": {}, "出": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "层": {"docs": {}, "提": {"docs": {}, "示": {"docs": {}, "完": {"docs": {}, "美": {"docs": {}, "的": {"docs": {}, "线": {"docs": {}, "上": {"docs": {}, "用": {"docs": {}, "户": {"docs": {}, "体": {"docs": {}, "验": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "开": {"docs": {}, "源": {"docs": {}, "项": {"docs": {}, "目": {"docs": {}, "不": {"docs": {}, "易": {"docs": {}, "，": {"docs": {}, "若": {"docs": {}, "此": {"docs": {}, "项": {"docs": {}, "目": {"docs": {}, "能": {"docs": {}, "得": {"docs": {}, "到": {"docs": {}, "你": {"docs": {}, "的": {"docs": {}, "青": {"docs": {}, "睐": {"docs": {}, "，": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "捐": {"docs": {}, "赠": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "作": {"docs": {}, "者": {"docs": {}, "持": {"docs": {}, "续": {"docs": {}, "开": {"docs": {}, "发": {"docs": {}, "与": {"docs": {}, "维": {"docs": {}, "护": {"docs": {}, "，": {"docs": {}, "感": {"docs": {}, "谢": {"docs": {}, "所": {"docs": {}, "有": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "开": {"docs": {}, "源": {"docs": {}, "的": {"docs": {}, "朋": {"docs": {}, "友": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "启": {"docs": {}, "后": {"docs": {}, "，": {"docs": {}, "会": {"docs": {}, "显": {"docs": {}, "示": {"docs": {}, "路": {"docs": {}, "由": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "，": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "后": {"docs": {}, "将": {"docs": {}, "定": {"docs": {}, "位": {"docs": {}, "到": {"docs": {}, "当": {"docs": {}, "前": {"docs": {}, "页": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {}, "不": {"docs": {}, "会": {"docs": {}, "有": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "问": {"docs": {}, "题": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}, "总": {"docs": {}, "体": {"docs": {}, "预": {"docs": {}, "览": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "技": {"docs": {}, "术": {"docs": {}, "交": {"docs": {}, "流": {"docs": {}, "q": {"docs": {}, "q": {"docs": {}, "群": {"docs": {}, "：": {"1": {"1": {"6": {"5": {"3": {"0": {"1": {"5": {"0": {"0": {"docs": {}, "、": {"6": {"6": {"7": {"8": {"1": {"3": {"2": {"4": {"9": {"docs": {}, "&": {"docs": {}, "#": {"docs": {}, "x": {"1": {"docs": {}, "f": {"2": {"3": {"5": {"docs": {}, ";": {"docs": {}, "、": {"5": {"6": {"1": {"8": {"3": {"8": {"0": {"8": {"6": {"docs": {}, "&": {"docs": {}, "#": {"docs": {}, "x": {"1": {"docs": {}, "f": {"2": {"3": {"5": {"docs": {}, ";": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}, "docs": {}}, "docs": {}}, "docs": {}}}, "docs": {}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}}, "docs": {}}, "docs": {}}, "docs": {}}}, "docs": {}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}}, "捐": {"docs": {}, "赠": {"docs": {}, "支": {"docs": {}, "持": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "支": {"docs": {}, "持": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "多": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "，": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "打": {"docs": {}, "开": {"docs": {}, "多": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}, "无": {"docs": {}, "限": {"docs": {}, "级": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "和": {"docs": {}, "对": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}, "效": {"docs": {}, "果": {"docs": {}, "预": {"docs": {}, "览": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}, "最": {"docs": {}, "简": {"docs": {}, "洁": {"docs": {}, "、": {"docs": {}, "清": {"docs": {}, "爽": {"docs": {}, "、": {"docs": {}, "易": {"docs": {}, "用": {"docs": {}, "的": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "模": {"docs": {}, "板": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}, "后": {"docs": {}, "将": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "t": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "的": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}, "大": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "打": {"docs": {}, "开": {"docs": {}, "数": {"docs": {}, "量": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}, "界": {"docs": {}, "面": {"docs": {}, "足": {"docs": {}, "够": {"docs": {}, "简": {"docs": {}, "洁": {"docs": {}, "清": {"docs": {}, "爽": {"docs": {}, "，": {"docs": {}, "响": {"docs": {}, "应": {"docs": {}, "式": {"docs": {}, "且": {"docs": {}, "适": {"docs": {}, "配": {"docs": {}, "手": {"docs": {}, "机": {"docs": {}, "端": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}, "简": {"docs": {}, "介": {"docs": {"./": {"ref": "./", "tf": 10}}}}, "页": {"docs": {}, "面": {"docs": {}, "支": {"docs": {}, "持": {"docs": {}, "多": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "，": {"docs": {}, "可": {"docs": {}, "自": {"docs": {}, "行": {"docs": {}, "选": {"docs": {}, "择": {"docs": {}, "喜": {"docs": {}, "欢": {"docs": {}, "的": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}, "项": {"docs": {}, "目": {"docs": {}, "介": {"docs": {}, "绍": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}, "会": {"docs": {}, "不": {"docs": {}, "定": {"docs": {}, "时": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "更": {"docs": {}, "新": {"docs": {}, "，": {"docs": {}, "建": {"docs": {}, "议": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "和": {"docs": {}, "w": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "一": {"docs": {}, "份": {"docs": {}, "。": {"docs": {"./": {"ref": "./", "tf": 0.008264462809917356}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "'": {"0": {"docs": {}, "'": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.016129032258064516}}}}, "1": {"docs": {}, "'": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}, "docs": {}, "'": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}}, "_": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "f": {"docs": {}, "'": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "s": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "p": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "}": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}, "创": {"docs": {}, "建": {"docs": {}, "时": {"docs": {}, "间": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "删": {"docs": {}, "除": {"docs": {}, "时": {"docs": {}, "间": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "名": {"docs": {}, "称": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "备": {"docs": {}, "注": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "更": {"docs": {}, "新": {"docs": {}, "时": {"docs": {}, "间": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "父": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}, "状": {"docs": {}, "态": {"docs": {}, "(": {"0": {"docs": {}, ":": {"docs": {}, "禁": {"docs": {}, "用": {"docs": {}, ",": {"1": {"docs": {}, ":": {"docs": {}, "启": {"docs": {}, "用": {"docs": {}, ")": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}, "docs": {}}}}}}, "docs": {}}}}, "菜": {"docs": {}, "单": {"docs": {}, "图": {"docs": {}, "标": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "排": {"docs": {}, "序": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "链": {"docs": {}, "接": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}, "打": {"docs": {}, "开": {"docs": {}, "方": {"docs": {}, "式": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "c": {"docs": {}, "'": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}, "e": {"docs": {}, "r": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "'": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}, "首": {"docs": {}, "页": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "/": {"docs": {}, "r": {"docs": {}, "u": {"docs": {}, "g": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "r": {"docs": {}, "u": {"docs": {}, "g": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "g": {"docs": {}, "b": {"docs": {}, "(": {"1": {"9": {"1": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "a": {"docs": {}, "(": {"1": {"0": {"7": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}}}}, "docs": {}}, "6": {"0": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}, "#": {"0": {"docs": {}, "c": {"0": {"docs": {}, "f": {"1": {"3": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "docs": {}}, "docs": {}}, "c": {"0": {"docs": {}, "c": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}, "docs": {}}}, "docs": {}}}, "1": {"9": {"2": {"0": {"2": {"7": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "7": {"9": {"7": {"1": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}, "e": {"9": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}}}}, "docs": {}}, "a": {"docs": {}, "a": {"0": {"9": {"4": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}}}}, "docs": {}}, "docs": {}}, "docs": {}}}}, "2": {"3": {"2": {"6": {"2": {"docs": {}, "e": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "4": {"3": {"3": {"4": {"6": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "8": {"3": {"3": {"3": {"docs": {}, "e": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}, "f": {"4": {"0": {"5": {"6": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}, "3": {"docs": {}, "b": {"3": {"docs": {}, "f": {"4": {"docs": {}, "b": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}, "docs": {}}}, "docs": {}}}, "5": {"6": {"5": {"6": {"5": {"6": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "7": {"3": {"7": {"3": {"7": {"3": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}, "e": {"4": {"docs": {}, "e": {"4": {"docs": {}, "e": {"4": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "docs": {}}}, "docs": {}}}, "docs": {}}, "f": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}}}}}}}}}}}, "最": {"docs": {}, "终": {"docs": {}, "的": {"docs": {}, "提": {"docs": {}, "交": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "'": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}, "(": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "`": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "`": {"docs": {}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, "`": {"docs": {}, ")": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "`": {"docs": {}, ")": {"docs": {}, ",": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}, "!": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "$": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, ")": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}, "$": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}, "a": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, ")": {"docs": {}, "$": {"docs": {}, "v": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}, "c": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}, "m": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "v": {"docs": {}, "o": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "e": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "s": {"docs": {}, "(": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, ".": {"docs": {}, "e": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "s": {"docs": {}, "(": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ")": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}, "v": {"docs": {}, "a": {"docs": {}, "r": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}}}}, ")": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.011312217194570135}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}, "iframe.html": {"ref": "iframe.html", "tf": 0.015267175572519083}, "onepage.html": {"ref": "onepage.html", "tf": 0.016835016835016835}}}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, ")": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}, ")": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}, "init/java.html": {"ref": "init/java.html", "tf": 0.005911330049261084}}}, "`": {"docs": {}, "c": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.016129032258064516}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "d": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.016129032258064516}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "`": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ":": {"docs": {}, "\"": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {}, ";": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, ":": {"docs": {}, "\"": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "_": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "w": {"docs": {}, ";": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ")": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.010344827586206896}}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}}}, "d": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, "`": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}}}}}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "t": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.0967741935483871}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "b": {"docs": {}, "u": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "a": {"docs": {}, "t": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "b": {"docs": {}, ":": {"docs": {}, ":": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "'": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}}}}}}}}}}}}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "'": {"docs": {}, ")": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}}}}}}}}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "a": {"docs": {}, "s": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, "e": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.008866995073891626}}, "b": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "k": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "k": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "p": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}, "d": {"docs": {}, "l": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "r": {"docs": {}, "i": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.024193548387096774}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.003940886699507389}}}}}}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "l": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.11290322580645161}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "e": {"docs": {}, "w": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.005911330049261084}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.0070921985815602835}}, "(": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "s": {"docs": {}, ".": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}, ".": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "后": {"docs": {}, "面": {"docs": {}, "p": {"docs": {}, "h": {"docs": {}, "p": {"docs": {}, "、": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "的": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "都": {"docs": {}, "是": {"docs": {}, "基": {"docs": {}, "于": {"docs": {}, "该": {"docs": {}, "表": {"docs": {}, "结": {"docs": {}, "构": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}}}}}}}}}}}}, "续": {"docs": {}, "此": {"docs": {}, "处": {"docs": {}, "加": {"docs": {}, "上": {"docs": {}, "用": {"docs": {}, "户": {"docs": {}, "的": {"docs": {}, "权": {"docs": {}, "限": {"docs": {}, "判": {"docs": {}, "断": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "台": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "方": {"docs": {}, "案": {"docs": {}, "配": {"docs": {}, "色": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "模": {"docs": {}, "板": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "示": {"docs": {}, "例": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 10}}}}}}, "表": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {}, "表": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "示": {"docs": {}, "例": {"docs": {"init/sql.html": {"ref": "init/sql.html", "tf": 0.008064516129032258}}}}}}}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "对": {"docs": {}, "象": {"docs": {}, "如": {"docs": {}, "下": {"docs": {}, "：": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}, "$": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}}, ")": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}}}, "{": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}, "n": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "[": {"docs": {}, "'": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, "]": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}, "t": {"docs": {}, "h": {"docs": {}, "i": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.024}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.02459016393442623}}}}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "[": {"docs": {}, "]": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}, "v": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, ";": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "[": {"docs": {}, "'": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ")": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}, "/": {"docs": {}, "/": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.033936651583710405}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.03672316384180791}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}, "递": {"docs": {}, "归": {"docs": {}, "获": {"docs": {}, "取": {"docs": {}, "子": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}, "这": {"docs": {}, "里": {"docs": {}, "我": {"docs": {}, "只": {"docs": {}, "查": {"docs": {}, "询": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "转": {"docs": {}, "态": {"docs": {}, "为": {"docs": {}, "启": {"docs": {}, "用": {"docs": {}, ",": {"docs": {}, "可": {"docs": {}, "自": {"docs": {}, "行": {"docs": {}, "定": {"docs": {}, "义": {"docs": {}, "和": {"docs": {}, "写": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}, "是": {"docs": {}, "常": {"docs": {}, "规": {"docs": {}, "写": {"docs": {}, "法": {"docs": {}, "不": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "b": {"docs": {}, "o": {"docs": {}, "k": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, "/": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.16784869976359337}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "字": {"docs": {}, "体": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}, "背": {"docs": {}, "景": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "选": {"docs": {}, "项": {"docs": {}, "卡": {"docs": {}, "选": {"docs": {}, "中": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}, "头": {"docs": {}, "部": {"docs": {}, "右": {"docs": {}, "侧": {"docs": {}, "下": {"docs": {}, "拉": {"docs": {}, "字": {"docs": {}, "体": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "字": {"docs": {}, "体": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}, "更": {"docs": {}, "多": {"docs": {}, "下": {"docs": {}, "拉": {"docs": {}, "列": {"docs": {}, "表": {"docs": {}, "字": {"docs": {}, "体": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}, "选": {"docs": {}, "中": {"docs": {}, "背": {"docs": {}, "景": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "背": {"docs": {}, "景": {"docs": {}, "色": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "选": {"docs": {}, "中": {"docs": {}, "背": {"docs": {}, "景": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}, "鼠": {"docs": {}, "标": {"docs": {}, "选": {"docs": {}, "中": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "缩": {"docs": {}, "放": {"docs": {}, "按": {"docs": {}, "钮": {"docs": {}, "样": {"docs": {}, "式": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}, "左": {"docs": {}, "侧": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "子": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "背": {"docs": {}, "景": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}, "字": {"docs": {}, "体": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}, "更": {"docs": {}, "多": {"docs": {}, "下": {"docs": {}, "拉": {"docs": {}, "样": {"docs": {}, "式": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}, "背": {"docs": {}, "景": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}, "选": {"docs": {}, "中": {"docs": {}, "字": {"docs": {}, "体": {"docs": {}, "颜": {"docs": {}, "色": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}, "背": {"docs": {}, "景": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}, "监": {"docs": {}, "听": {"docs": {}, "提": {"docs": {}, "交": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, "*": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "*": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.012807881773399015}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "复": {"docs": {}, "合": {"docs": {}, "主": {"docs": {}, "键": {"docs": {}, "要": {"docs": {}, "用": {"docs": {}, "这": {"docs": {}, "个": {"docs": {}, "注": {"docs": {}, "解": {"docs": {}, "*": {"docs": {}, "/": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "i": {"docs": {}, "n": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "/": {"docs": {}, "*": {"docs": {}, "*": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, ">": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "b": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"0": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}, "docs": {}, "$": {"docs": {}, "v": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}, "[": {"docs": {}, "'": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}}}}}}}}}}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}, "i": {"docs": {}, "d": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "$": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "[": {"docs": {}, "'": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "'": {"docs": {}, ",": {"docs": {}, "'": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ")": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, ")": {"docs": {}, ";": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}, "w": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "'": {"docs": {}, ",": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}, "f": {"docs": {}, "i": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "'": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, ",": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, ",": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, ",": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, ",": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, ",": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "'": {"docs": {}, ")": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "[": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.016}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.01639344262295082}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.011450381679389313}, "onepage.html": {"ref": "onepage.html", "tf": 0.010101010101010102}}, "]": {"docs": {}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}, "*": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.017241379310344827}}}}}}}}}}}}}}}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "]": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "]": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.0070921985815602835}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "e": {"docs": {}, "_": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "_": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {}, "]": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "]": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, ";": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.024}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.02459016393442623}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}, "{": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.024}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.02459016393442623}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.04827586206896552}, "init/java.html": {"ref": "init/java.html", "tf": 0.061083743842364535}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.08274231678486997}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.038461538461538464}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.04519774011299435}, "iframe.html": {"ref": "iframe.html", "tf": 0.0648854961832061}, "onepage.html": {"ref": "onepage.html", "tf": 0.06734006734006734}}}, "}": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.056}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.05737704918032787}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.05172413793103448}, "init/java.html": {"ref": "init/java.html", "tf": 0.060098522167487685}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.07801418439716312}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.011299435028248588}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.016835016835016835}}, "*": {"docs": {}, "/": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.01583710407239819}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.011299435028248588}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.02356902356902357}}}, ".": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "{": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}, "u": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "[": {"docs": {}, "'": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "'": {"docs": {}, "]": {"docs": {}, ",": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}}}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.013574660633484163}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.01694915254237288}, "iframe.html": {"ref": "iframe.html", "tf": 0.019083969465648856}, "onepage.html": {"ref": "onepage.html", "tf": 0.020202020202020204}}}}, "为": {"docs": {}, "了": {"docs": {}, "方": {"docs": {}, "便": {"docs": {}, "演": {"docs": {}, "示": {"docs": {}, "，": {"docs": {}, "直": {"docs": {}, "接": {"docs": {}, "用": {"docs": {}, "d": {"docs": {}, "b": {"docs": {}, "类": {"docs": {}, "去": {"docs": {}, "写": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}, "获": {"docs": {}, "取": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "列": {"docs": {}, "表": {"docs": {"init/laravel.html": {"ref": "init/laravel.html", "tf": 0.008}, "init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}, "init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "$": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/thinkphp.html": {"ref": "init/thinkphp.html", "tf": 0.00819672131147541}}}}}}}}}}}}}}}}}}, "模": {"docs": {}, "块": {"docs": {}, "的": {"docs": {}, "拆": {"docs": {}, "分": {"docs": {}, "，": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "更": {"docs": {}, "加": {"docs": {}, "规": {"docs": {}, "范": {"docs": {}, "化": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "（": {"docs": {}, "s": {"docs": {}, "p": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "）": {"docs": {}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "（": {"docs": {}, "s": {"docs": {}, "p": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "）": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "示": {"docs": {}, "例": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "p": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 10}}}}}}}}}}}}}}}, "d": {"docs": {}, "b": {"docs": {}, "c": {"docs": {}, ":": {"docs": {}, "m": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "q": {"docs": {}, "l": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"1": {"2": {"7": {"docs": {}, ".": {"0": {"docs": {}, ".": {"0": {"docs": {}, ".": {"1": {"docs": {}, ":": {"3": {"3": {"0": {"6": {"docs": {}, "/": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {}, "名": {"docs": {}, "称": {"docs": {}, "?": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "c": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "=": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "f": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}, "docs": {}}}, "docs": {}}}, "docs": {}}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "r": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}, "!": {"docs": {}, "=": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "*": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.015763546798029555}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "x": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.013793103448275862}}}}}}}}}}}}}, "/": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.011822660098522168}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}, "=": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.017241379310344827}}}}, "_": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}, ",": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}}}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, "\"": {"docs": {}, ")": {"docs": {}, ".": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "(": {"docs": {}, "&": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}}}}}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "s": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, ",": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}, "i": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.003940886699507389}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.05517241379310345}, "init/java.html": {"ref": "init/java.html", "tf": 0.026600985221674877}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.030732860520094562}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}, "u": {"docs": {}, "c": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.020689655172413793}}}}}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "y": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.02413793103448276}}, "(": {"docs": {}, ")": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, ".": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {}, ".": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "g": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "o": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.009456264775413711}}, "(": {"docs": {}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}}}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "i": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, ",": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "u": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, ")": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}}}}}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "r": {"docs": {}, "e": {"docs": {}, "p": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}, "y": {"docs": {}, ".": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "i": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, "e": {"docs": {}, ".": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, ";": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "i": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}, "c": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "=": {"docs": {}, "\"": {"docs": {}, "\"": {"docs": {}, "可": {"docs": {}, "删": {"docs": {}, "掉": {"docs": {}, "不": {"docs": {}, "必": {"docs": {}, "跟": {"docs": {}, "我": {"docs": {}, "，": {"docs": {}, "前": {"docs": {}, "提": {"docs": {}, "是": {"docs": {}, "你": {"docs": {}, "的": {"docs": {}, "表": {"docs": {}, "对": {"docs": {}, "应": {"docs": {}, "你": {"docs": {}, "的": {"docs": {}, "表": {"docs": {}, "空": {"docs": {}, "间": {"docs": {}, "就": {"docs": {}, "是": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "库": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "e": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "z": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.003940886699507389}}}}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "i": {"docs": {}, "c": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "e": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "p": {"docs": {}, "l": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "a": {"docs": {}, "v": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "t": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "l": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}, "d": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.002955665024630542}}}}}}}}}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "k": {"docs": {}, "e": {"docs": {}, "y": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}, "p": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "u": {"docs": {}, "s": {"docs": {}, "(": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "g": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}, "n": {"docs": {}, "g": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "都": {"docs": {}, "有": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "(": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, ";": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.05673758865248227}}}}}, "h": {"docs": {}, "o": {"docs": {}, "w": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "p": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "r": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "b": {"docs": {}, "o": {"docs": {}, "o": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "w": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "k": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}, "q": {"docs": {}, "l": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "u": {"docs": {}, "f": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "x": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "l": {"docs": {}, "i": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "\"": {"docs": {}, ",": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "后": {"docs": {}, "台": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "接": {"docs": {}, "口": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}}, "结": {"docs": {}, "构": {"docs": {}, "体": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "目": {"docs": {}, "录": {"docs": {}, "下": {"docs": {}, "的": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "或": {"docs": {}, "者": {"docs": {}, "查": {"docs": {}, "看": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "的": {"docs": {}, "第": {"docs": {}, "二": {"docs": {}, "点": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}, "地": {"docs": {}, "址": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "目": {"docs": {}, "录": {"docs": {}, "下": {"docs": {}, "的": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "或": {"docs": {}, "者": {"docs": {}, "查": {"docs": {}, "看": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "的": {"docs": {}, "第": {"docs": {}, "二": {"docs": {}, "点": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "说": {"docs": {}, "明": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "加": {"docs": {}, "载": {"docs": {}, "时": {"docs": {}, "间": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}, "接": {"docs": {}, "口": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}, "表": {"docs": {}, "单": {"docs": {}, "，": {"docs": {}, "要": {"docs": {}, "加": {"docs": {}, "上": {"docs": {}, "，": {"docs": {}, "不": {"docs": {}, "然": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "部": {"docs": {}, "分": {"docs": {}, "组": {"docs": {}, "件": {"docs": {}, "可": {"docs": {}, "能": {"docs": {}, "会": {"docs": {}, "不": {"docs": {}, "加": {"docs": {}, "载": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {}, "是": {"docs": {}, "否": {"docs": {}, "加": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, "号": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "对": {"docs": {}, "应": {"docs": {}, "m": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}}}}, "控": {"docs": {}, "制": {"docs": {}, "器": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}, "init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "e": {"docs": {}, "逻": {"docs": {}, "辑": {"docs": {}, "层": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}, "菜": {"docs": {}, "单": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.006896551724137931}}, "结": {"docs": {}, "构": {"docs": {}, "体": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, "果": {"docs": {}, "对": {"docs": {}, "象": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "图": {"docs": {}, "标": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "样": {"docs": {}, "式": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "排": {"docs": {}, "序": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "表": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}, "栏": {"docs": {}, "建": {"docs": {}, "议": {"docs": {}, "最": {"docs": {}, "多": {"docs": {}, "四": {"docs": {}, "级": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "，": {"docs": {}, "四": {"docs": {}, "级": {"docs": {}, "以": {"docs": {}, "后": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "显": {"docs": {}, "示": {"docs": {}, "并": {"docs": {}, "没": {"docs": {}, "有": {"docs": {}, "那": {"docs": {}, "么": {"docs": {}, "友": {"docs": {}, "好": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "首": {"docs": {}, "页": {"docs": {"init/golang.html": {"ref": "init/golang.html", "tf": 0.0034482758620689655}}}}, "#": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "x": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "d": {"docs": {}, "d": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}, "g": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, ":": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}, "t": {"docs": {}, "h": {"docs": {}, "y": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "f": {"docs": {}, "模": {"docs": {}, "板": {"docs": {}, "配": {"docs": {}, "置": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}, "严": {"docs": {}, "格": {"docs": {}, "执": {"docs": {}, "行": {"docs": {}, "h": {"5": {"docs": {}, "标": {"docs": {}, "准": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "docs": {}}}}}}, "关": {"docs": {}, "掉": {"docs": {}, "原": {"docs": {}, "生": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "图": {"docs": {}, "标": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "最": {"docs": {}, "大": {"2": {"2": {"0": {"docs": {}, "个": {"docs": {}, "并": {"docs": {}, "发": {"docs": {}, "，": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "达": {"docs": {}, "到": {"docs": {}, "不": {"docs": {}, "丢": {"docs": {}, "包": {"docs": {}, "（": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "自": {"docs": {}, "己": {"docs": {}, "实": {"docs": {}, "测": {"docs": {}, "）": {"docs": {}, "，": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "为": {"2": {"0": {"0": {"docs": {}, "。": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}, "docs": {}}, "docs": {}}, "docs": {}}}, "本": {"docs": {}, "地": {"docs": {}, "开": {"docs": {}, "发": {"docs": {}, "环": {"docs": {}, "境": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "中": {"docs": {}, "心": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "解": {"docs": {}, "决": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "其": {"docs": {}, "他": {"docs": {}, "库": {"docs": {}, "的": {"docs": {}, "表": {"docs": {}, "时": {"docs": {}, "候": {"docs": {}, "，": {"docs": {}, "把": {"docs": {}, "小": {"docs": {}, "数": {"docs": {}, "点": {"docs": {}, "变": {"docs": {}, "成": {"docs": {}, "下": {"docs": {}, "划": {"docs": {}, "线": {"docs": {}, "，": {"docs": {}, "导": {"docs": {}, "致": {"docs": {}, "s": {"docs": {}, "q": {"docs": {}, "l": {"docs": {}, "无": {"docs": {}, "法": {"docs": {}, "成": {"docs": {}, "功": {"docs": {}, "执": {"docs": {}, "行": {"docs": {}, "。": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "设": {"docs": {}, "置": {"docs": {}, "t": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "c": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "参": {"docs": {}, "数": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "这": {"docs": {}, "是": {"docs": {}, "由": {"docs": {}, "于": {"docs": {}, "物": {"docs": {}, "理": {"docs": {}, "命": {"docs": {}, "名": {"docs": {}, "策": {"docs": {}, "略": {"docs": {}, "引": {"docs": {}, "起": {"docs": {}, "的": {"docs": {}, "，": {"docs": {}, "大": {"docs": {}, "写": {"docs": {}, "字": {"docs": {}, "母": {"docs": {}, "变": {"docs": {}, "小": {"docs": {}, "写": {"docs": {}, "，": {"docs": {}, "加": {"docs": {}, "_": {"docs": {}, "下": {"docs": {}, "划": {"docs": {}, "线": {"docs": {}, "（": {"docs": {}, "h": {"docs": {}, "i": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"5": {"docs": {}, "以": {"docs": {}, "上": {"docs": {}, "高": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, "）": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "连": {"docs": {}, "接": {"docs": {}, "超": {"docs": {}, "时": {"docs": {}, "，": {"docs": {}, "单": {"docs": {}, "位": {"docs": {}, "为": {"docs": {}, "毫": {"docs": {}, "秒": {"docs": {}, "，": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}, "配": {"docs": {}, "置": {"docs": {}, "日": {"docs": {}, "志": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "参": {"docs": {}, "数": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "访": {"docs": {}, "问": {"docs": {}, "路": {"docs": {}, "径": {"docs": {}, "，": {"docs": {}, "默": {"docs": {}, "认": {"docs": {}, "为": {"docs": {}, "/": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}, "静": {"docs": {}, "态": {"docs": {}, "资": {"docs": {}, "源": {"docs": {}, "路": {"docs": {}, "径": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "项": {"docs": {}, "目": {"docs": {}, "端": {"docs": {}, "口": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, ",": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, ".": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {}, ".": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.007633587786259542}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}}}, "@": {"docs": {}, "a": {"docs": {}, "u": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "m": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.011822660098522168}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}, "e": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}, "e": {"docs": {}, "s": {"docs": {}, "c": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "p": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "y": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "c": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "t": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "u": {"docs": {}, "p": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "e": {"docs": {}, "m": {"docs": {}, "b": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}, "e": {"docs": {}, "d": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}}, "n": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "g": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "d": {"docs": {}, "v": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "g": {"docs": {}, "i": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "/": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "u": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "_": {"docs": {}, "n": {"docs": {}, "u": {"docs": {}, "l": {"docs": {}, "l": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "o": {"docs": {}, "v": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "y": {"docs": {}, "(": {"docs": {}, "v": {"docs": {}, "a": {"docs": {}, "l": {"docs": {}, "u": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "q": {"docs": {}, "u": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "m": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "g": {"docs": {}, "(": {"docs": {}, "\"": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "\"": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}, "s": {"docs": {}, "o": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}, "t": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "r": {"docs": {}, "o": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "v": {"docs": {}, "i": {"docs": {}, "c": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "t": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0019704433497536944}}}}}}}}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "l": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}, "e": {"docs": {}, "(": {"docs": {}, "n": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "y": {"docs": {}, "m": {"docs": {}, "l": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "文": {"docs": {}, "件": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}, "创": {"docs": {}, "建": {"docs": {}, "时": {"docs": {}, "间": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "a": {"docs": {}, "s": {"docs": {}, "p": {"docs": {}, ".": {"docs": {}, "n": {"docs": {}, "e": {"docs": {}, "t": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}, "一": {"docs": {}, "个": {"docs": {}, "根": {"docs": {}, "对": {"docs": {}, "象": {"docs": {}, "来": {"docs": {}, "接": {"docs": {}, "受": {"docs": {}, "处": {"docs": {}, "理": {"docs": {}, "好": {"docs": {}, "的": {"docs": {}, "数": {"docs": {}, "据": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}, "删": {"docs": {}, "掉": {"docs": {}, "p": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "s": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "_": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "y": {"docs": {}, ".": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}}}}}}}}}}}}, "除": {"docs": {}, "时": {"docs": {}, "间": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}, "名": {"docs": {}, "称": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "备": {"docs": {}, "注": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "信": {"docs": {}, "息": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, "复": {"docs": {}, "合": {"docs": {}, "主": {"docs": {}, "键": {"docs": {}, "要": {"docs": {}, "用": {"docs": {}, "这": {"docs": {}, "个": {"docs": {}, "注": {"docs": {}, "解": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}}}}}, "更": {"docs": {}, "新": {"docs": {}, "时": {"docs": {}, "间": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}, "父": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}, "级": {"docs": {}, "i": {"docs": {}, "d": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}}}}}, "状": {"docs": {}, "态": {"docs": {}, "(": {"0": {"docs": {}, ":": {"docs": {}, "禁": {"docs": {}, "用": {"docs": {}, ",": {"1": {"docs": {}, ":": {"docs": {}, "启": {"docs": {}, "用": {"docs": {}, ")": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}, "docs": {}}}}}}, "docs": {}}}}, "示": {"docs": {}, "例": {"docs": {}, "提": {"docs": {}, "供": {"docs": {}, "来": {"docs": {}, "源": {"docs": {}, "：": {"docs": {}, "香": {"docs": {}, "喷": {"docs": {}, "喷": {"docs": {}, "的": {"docs": {}, "如": {"docs": {}, "歌": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "a": {"0": {"docs": {}, "~": {"docs": {}, "海": {"docs": {}, "阔": {"docs": {}, "天": {"docs": {}, "空": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}, "docs": {}}}}}}}, "在": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, "/": {"docs": {}, "w": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "u": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "r": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.006734006734006734}}}}}}}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.01583710407239819}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.014124293785310734}, "iframe.html": {"ref": "iframe.html", "tf": 0.026717557251908396}, "onepage.html": {"ref": "onepage.html", "tf": 0.026936026936026935}}}}}}, "链": {"docs": {}, "接": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}, "init/netcore.html": {"ref": "init/netcore.html", "tf": 0.004728132387706856}}, "打": {"docs": {}, "开": {"docs": {}, "方": {"docs": {}, "式": {"docs": {"init/java.html": {"ref": "init/java.html", "tf": 0.0009852216748768472}}}}}}}}, "|": {"docs": {}, "|": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "子": {"docs": {}, "集": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "完": {"docs": {}, "整": {"docs": {}, "的": {"docs": {}, "后": {"docs": {}, "端": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "：": {"docs": {}, "h": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "p": {"docs": {}, "s": {"docs": {}, ":": {"docs": {}, "/": {"docs": {}, "/": {"docs": {}, "g": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "u": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "/": {"docs": {}, "c": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "y": {"docs": {}, "i": {"2": {"0": {"0": {"6": {"5": {"2": {"0": {"docs": {}, "/": {"docs": {}, "s": {"docs": {}, "y": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "u": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}, "docs": {}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "序": {"docs": {}, "号": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "排": {"docs": {}, "序": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "新": {"docs": {}, "开": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "方": {"docs": {}, "式": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}, "增": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "选": {"docs": {}, "项": {"docs": {}, "卡": {"docs": {}, "的": {"docs": {}, "切": {"docs": {}, "换": {"docs": {}, "与": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "之": {"docs": {}, "间": {"docs": {}, "的": {"docs": {}, "联": {"docs": {}, "动": {"docs": {}, "功": {"docs": {}, "能": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "加": {"docs": {}, "载": {"docs": {}, "层": {"docs": {}, "，": {"docs": {}, "更": {"docs": {}, "好": {"docs": {}, "的": {"docs": {}, "视": {"docs": {}, "觉": {"docs": {}, "体": {"docs": {}, "验": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "时": {"docs": {}, "一": {"docs": {}, "个": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "项": {"docs": {}, "完": {"docs": {}, "成": {"docs": {}, "多": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "和": {"docs": {}, "单": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "之": {"docs": {}, "间": {"docs": {}, "的": {"docs": {}, "切": {"docs": {}, "换": {"docs": {}, "，": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "的": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "还": {"docs": {}, "是": {"docs": {}, "一": {"docs": {}, "样": {"docs": {}, "的": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {}, "是": {"docs": {}, "否": {"docs": {}, "加": {"docs": {}, "版": {"docs": {}, "本": {"docs": {}, "号": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}, "菜": {"docs": {}, "单": {"docs": {}, "在": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "的": {"docs": {}, "时": {"docs": {}, "候": {"docs": {}, "可": {"docs": {}, "以": {"docs": {}, "展": {"docs": {}, "开": {"docs": {}, "子": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}, "是": {"docs": {}, "否": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "开": {"docs": {}, "启": {"docs": {}, "多": {"docs": {}, "模": {"docs": {}, "块": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}, "打": {"docs": {}, "开": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "定": {"docs": {}, "位": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}, "默": {"docs": {}, "认": {"docs": {}, "展": {"docs": {}, "开": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}}}}}}}, "判": {"docs": {}, "断": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "l": {"docs": {}, "有": {"docs": {}, "效": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}, "l": {"docs": {}, "o": {"docs": {}, "g": {"docs": {}, "o": {"docs": {}, "信": {"docs": {}, "息": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}, "头": {"docs": {}, "部": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "和": {"docs": {}, "左": {"docs": {}, "侧": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "对": {"docs": {}, "应": {"docs": {}, "的": {"docs": {}, "信": {"docs": {}, "息": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}, "首": {"docs": {}, "页": {"docs": {}, "信": {"docs": {}, "息": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}, "权": {"docs": {}, "限": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "树": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "树": {"docs": {}, "结": {"docs": {}, "构": {"docs": {}, "对": {"docs": {}, "象": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "节": {"docs": {}, "点": {"docs": {}, "名": {"docs": {}, "称": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}, "地": {"docs": {}, "址": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}, "赋": {"docs": {}, "值": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "给": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "返": {"docs": {}, "回": {"docs": {}, "给": {"docs": {}, "前": {"docs": {}, "端": {"docs": {}, "就": {"docs": {}, "行": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}, "参": {"docs": {}, "数": {"docs": {}, "对": {"docs": {}, "应": {"docs": {}, "的": {"docs": {}, "事": {"docs": {}, "例": {"docs": {}, "(": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "：": {"0": {"docs": {}, "，": {"docs": {}, "清": {"docs": {}, "除": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "失": {"docs": {}, "败": {"docs": {}, "；": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "：": {"1": {"docs": {}, "，": {"docs": {}, "表": {"docs": {}, "示": {"docs": {}, "清": {"docs": {}, "除": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "成": {"docs": {}, "功": {"docs": {}, "；": {"docs": {}, ")": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}, "docs": {}}}}}}}}}}}}}}}}, "递": {"docs": {}, "归": {"docs": {}, "处": {"docs": {}, "理": {"docs": {}, "数": {"docs": {}, "据": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}, "库": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "如": {"docs": {}, "下": {"docs": {}, "，": {"docs": {"init/netcore.html": {"ref": "init/netcore.html", "tf": 0.002364066193853428}}}}}}}}}}}}}}}}}}}}}, "个": {"docs": {}, "人": {"docs": {}, "建": {"docs": {}, "议": {"docs": {}, "关": {"docs": {}, "闭": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "开": {"docs": {}, "启": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}, "以": {"docs": {}, "下": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "是": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, ".": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "时": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "传": {"docs": {}, "入": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "优": {"docs": {}, "化": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "方": {"docs": {}, "案": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "时": {"docs": {}, "的": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "数": {"docs": {}, "据": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "，": {"docs": {}, "以": {"docs": {}, "适": {"docs": {}, "配": {"docs": {}, "单": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "的": {"docs": {}, "切": {"docs": {}, "换": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "手": {"docs": {}, "机": {"docs": {}, "端": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "时": {"docs": {}, "的": {"docs": {}, "自": {"docs": {}, "适": {"docs": {}, "应": {"docs": {}, "，": {"docs": {}, "不": {"docs": {}, "会": {"docs": {}, "出": {"docs": {}, "现": {"docs": {}, "闪": {"docs": {}, "动": {"docs": {}, "的": {"docs": {}, "问": {"docs": {}, "题": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}, "返": {"docs": {}, "回": {"docs": {}, "主": {"docs": {}, "页": {"docs": {}, "按": {"docs": {}, "钮": {"docs": {}, "以": {"docs": {}, "及": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "导": {"docs": {}, "航": {"docs": {}, "栏": {"docs": {}, "的": {"docs": {}, "实": {"docs": {}, "现": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}, "使": {"docs": {}, "用": {"docs": {}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}, "单": {"docs": {}, "页": {"docs": {}, "面": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}, "修": {"docs": {}, "改": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "后": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "未": {"docs": {}, "生": {"docs": {}, "效": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "尝": {"docs": {}, "试": {"docs": {}, "清": {"docs": {}, "除": {"docs": {}, "浏": {"docs": {}, "览": {"docs": {}, "器": {"docs": {}, "缓": {"docs": {}, "存": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}, "参": {"docs": {}, "数": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}, "说": {"docs": {}, "明": {"docs": {}, "（": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "：": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "链": {"docs": {}, "接": {"docs": {}, "，": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "：": {"docs": {}, "标": {"docs": {}, "题": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}, "d": {"docs": {}, "a": {"docs": {}, "t": {"docs": {}, "a": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}, "基": {"docs": {}, "本": {"docs": {}, "资": {"docs": {}, "料": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "础": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "一": {"docs": {}, "览": {"docs": {}, "表": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}, "如": {"docs": {}, "需": {"docs": {}, "在": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "下": {"docs": {}, "方": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "。": {"docs": {}, "（": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}, "需": {"docs": {}, "要": {"docs": {}, "引": {"docs": {}, "入": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "页": {"docs": {}, "面": {"docs": {}, "中": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "下": {"docs": {}, "方": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "。": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}, "（": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "引": {"docs": {}, "入": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "j": {"docs": {}, "s": {"docs": {}, "局": {"docs": {}, "部": {"docs": {}, "刷": {"docs": {}, "新": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "下": {"docs": {}, "方": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "。": {"docs": {}, "（": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "引": {"docs": {}, "入": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "跳": {"docs": {}, "转": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "下": {"docs": {}, "方": {"docs": {}, "代": {"docs": {}, "码": {"docs": {}, "。": {"docs": {}, "（": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "引": {"docs": {}, "入": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "）": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "添": {"docs": {}, "加": {"docs": {}, "更": {"docs": {}, "多": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "信": {"docs": {}, "息": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "在": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "/": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}, "实": {"docs": {}, "际": {"docs": {}, "使": {"docs": {}, "用": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "对": {"docs": {}, "接": {"docs": {}, "后": {"docs": {}, "端": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "生": {"docs": {}, "成": {"docs": {}, "，": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "请": {"docs": {}, "参": {"docs": {}, "考": {"docs": {}, "文": {"docs": {}, "件": {"docs": {}, "：": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "/": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, ".": {"docs": {}, "j": {"docs": {}, "s": {"docs": {}, "o": {"docs": {}, "n": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.004524886877828055}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.005649717514124294}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "常": {"docs": {}, "见": {"docs": {}, "问": {"docs": {}, "题": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, "建": {"docs": {}, "议": {"0": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}, "docs": {}}}, "引": {"docs": {}, "入": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}, "iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "模": {"docs": {}, "块": {"docs": {}, "，": {"docs": {}, "根": {"docs": {}, "据": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "传": {"docs": {}, "入": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "，": {"docs": {}, "执": {"docs": {}, "行": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "a": {"docs": {}, "d": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, ".": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "r": {"docs": {}, "(": {"docs": {}, "o": {"docs": {}, "p": {"docs": {}, "t": {"docs": {}, "i": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "s": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "打": {"docs": {}, "开": {"docs": {}, "新": {"docs": {}, "的": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}, "方": {"docs": {}, "法": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}, "一": {"docs": {}, "：": {"docs": {}, "添": {"docs": {}, "加": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "s": {"docs": {}, "样": {"docs": {}, "式": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}, "二": {"docs": {}, "：": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "h": {"docs": {}, "o": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {}, "，": {"docs": {}, "示": {"docs": {}, "例": {"docs": {}, "在": {"docs": {}, "u": {"docs": {}, "s": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "内": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "请": {"docs": {}, "填": {"docs": {}, "写": {"docs": {}, "动": {"docs": {}, "态": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "地": {"docs": {}, "址": {"docs": {}, "。": {"docs": {}, "（": {"docs": {}, "实": {"docs": {}, "际": {"docs": {}, "应": {"docs": {}, "用": {"docs": {}, "中": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "以": {"docs": {}, "后": {"docs": {}, "端": {"docs": {}, "a": {"docs": {}, "p": {"docs": {}, "i": {"docs": {}, "接": {"docs": {}, "口": {"docs": {}, "方": {"docs": {}, "式": {"docs": {}, "去": {"docs": {}, "实": {"docs": {}, "现": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "添": {"docs": {}, "加": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "或": {"docs": {}, "者": {"docs": {}, "切": {"docs": {}, "换": {"docs": {}, "时": {"docs": {}, "的": {"docs": {}, "过": {"docs": {}, "渡": {"docs": {}, "动": {"docs": {}, "漫": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}, "类": {"docs": {}, "型": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "系": {"docs": {}, "统": {"docs": {}, "已": {"docs": {}, "内": {"docs": {}, "置": {"1": {"2": {"docs": {}, "套": {"docs": {}, "主": {"docs": {}, "题": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "，": {"docs": {}, "如": {"docs": {}, "果": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "自": {"docs": {}, "定": {"docs": {}, "义": {"docs": {}, "皮": {"docs": {}, "肤": {"docs": {}, "配": {"docs": {}, "色": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "在": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "h": {"docs": {}, "e": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "内": {"docs": {}, "按": {"docs": {}, "相": {"docs": {}, "同": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "添": {"docs": {}, "加": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}, "b": {"docs": {}, "g": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "内": {"docs": {}, "按": {"docs": {}, "相": {"docs": {}, "同": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "添": {"docs": {}, "加": {"docs": {}, "。": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "b": {"docs": {}, "g": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "r": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "内": {"docs": {}, "按": {"docs": {}, "相": {"docs": {}, "同": {"docs": {}, "格": {"docs": {}, "式": {"docs": {}, "添": {"docs": {}, "加": {"docs": {}, "。": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "docs": {}}, "docs": {}}}}}}, "缓": {"docs": {}, "存": {"docs": {}, "清": {"docs": {}, "理": {"docs": {}, "接": {"docs": {}, "口": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.006787330316742082}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.00847457627118644}}, "返": {"docs": {}, "回": {"docs": {}, "的": {"docs": {}, "参": {"docs": {}, "数": {"docs": {}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}, "说": {"docs": {}, "明": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}, "调": {"docs": {}, "用": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "监": {"docs": {}, "听": {"docs": {}, "：": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}, "p": {"docs": {}, "a": {"docs": {}, "g": {"docs": {}, "e": {"docs": {}, ".": {"docs": {}, "l": {"docs": {}, "i": {"docs": {}, "s": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {}, "(": {"docs": {}, "备": {"docs": {}, "注": {"docs": {}, "：": {"docs": {}, "框": {"docs": {}, "架": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "时": {"docs": {}, "已": {"docs": {}, "经": {"docs": {}, "进": {"docs": {}, "行": {"docs": {}, "监": {"docs": {}, "听": {"docs": {}, "，": {"docs": {}, "一": {"docs": {}, "般": {"docs": {}, "情": {"docs": {}, "况": {"docs": {}, "下": {"docs": {}, "不": {"docs": {}, "需": {"docs": {}, "要": {"docs": {}, "再": {"docs": {}, "次": {"docs": {}, "操": {"docs": {}, "作": {"docs": {}, ")": {"docs": {"onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "：": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, ".": {"docs": {}, "d": {"docs": {}, "e": {"docs": {}, "l": {"docs": {}, "e": {"docs": {}, "t": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "b": {"docs": {}, "y": {"docs": {}, "i": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "a": {"docs": {}, "m": {"docs": {}, "e": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "l": {"docs": {}, "o": {"docs": {}, "s": {"docs": {}, "e": {"docs": {}, "c": {"docs": {}, "u": {"docs": {}, "r": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "n": {"docs": {}, "t": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}}}}}}}}}}}}}}}}}}}}, "h": {"docs": {}, "a": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "(": {"docs": {}, "h": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}, "r": {"docs": {}, "e": {"docs": {}, "f": {"docs": {}, "r": {"docs": {}, "e": {"docs": {}, "s": {"docs": {}, "h": {"docs": {}, "(": {"docs": {}, ")": {"docs": {}, ";": {"docs": {"onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}, "配": {"docs": {}, "置": {"docs": {}, "项": {"docs": {}, "移": {"docs": {}, "出": {"docs": {}, "到": {"docs": {}, "外": {"docs": {}, "部": {"docs": {}, "的": {"docs": {}, "初": {"docs": {}, "始": {"docs": {}, "化": {"docs": {}, "配": {"docs": {}, "置": {"docs": {}, "里": {"docs": {}, "面": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}, "重": {"docs": {}, "构": {"docs": {}, "手": {"docs": {}, "机": {"docs": {}, "端": {"docs": {}, "左": {"docs": {}, "侧": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "，": {"docs": {}, "弹": {"docs": {}, "出": {"docs": {}, "菜": {"docs": {}, "单": {"docs": {}, "时": {"docs": {}, "不": {"docs": {}, "会": {"docs": {}, "挤": {"docs": {}, "压": {"docs": {}, "内": {"docs": {}, "容": {"docs": {}, "内": {"docs": {}, "面": {"docs": {}, "。": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}}}}}}}}}}}}}}}}}}}}}}}}, "防": {"docs": {}, "止": {"docs": {}, "打": {"docs": {}, "开": {"docs": {}, "太": {"docs": {}, "多": {"docs": {}, "的": {"docs": {}, "t": {"docs": {}, "a": {"docs": {}, "b": {"docs": {}, "窗": {"docs": {}, "口": {"docs": {}, "导": {"docs": {}, "致": {"docs": {}, "页": {"docs": {}, "面": {"docs": {}, "卡": {"docs": {}, "死": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}}}}}}}}}}}}}}}}}}}}, "默": {"docs": {}, "认": {"docs": {}, "值": {"docs": {"iframe-v2.html": {"ref": "iframe-v2.html", "tf": 0.0022624434389140274}, "onepage-v2.html": {"ref": "onepage-v2.html", "tf": 0.002824858757062147}}}, "皮": {"docs": {}, "肤": {"docs": {}, "（": {"0": {"docs": {}, "开": {"docs": {}, "始": {"docs": {}, "）": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}, "docs": {}}}}, "配": {"docs": {}, "置": {"docs": {}, "在": {"docs": {}, "l": {"docs": {}, "a": {"docs": {}, "y": {"docs": {}, "u": {"docs": {}, "i": {"docs": {}, "m": {"docs": {}, "i": {"docs": {}, "n": {"docs": {}, "i": {"docs": {}, ".": {"docs": {}, "c": {"docs": {}, "o": {"docs": {}, "n": {"docs": {}, "f": {"docs": {}, "i": {"docs": {}, "g": {"docs": {}, "方": {"docs": {}, "法": {"docs": {}, "内": {"docs": {}, "，": {"docs": {}, "请": {"docs": {}, "自": {"docs": {}, "行": {"docs": {}, "修": {"docs": {}, "改": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "说": {"docs": {}, "明": {"docs": {"iframe.html": {"ref": "iframe.html", "tf": 0.003816793893129771}, "onepage.html": {"ref": "onepage.html", "tf": 0.003367003367003367}}}}}}}}}, "length": 1865}, "corpusTokens": ["!=", "\"\"", "\"\",", "\"\";", "\",nativequeri", "\"_self\")", "\"_self\",", "\"_self\";", "\"api/clear.json\"", "\"api/clear.json\",", "\"api/init.json\",", "\"beegoadmin/models\"", "\"child\":", "\"child\":[...]", "\"clearinfo\":", "\"clearurl\":", "\"code\":", "\"create_at\"", "\"currency\":", "\"delete_at\"", "\"fa", "\"github.com/astaxie/beego\"", "\"github.com/astaxie/beego/orm\"", "\"homeinfo\":", "\"href\")", "\"href\":", "\"icon\")", "\"icon\":", "\"id\")", "\"image\":", "\"images/logo.png\"", "\"images/logo.png\",", "\"images/logo.png\";", "\"img\"", "\"js/\",", "\"layui", "\"<PERSON>ui<PERSON>\"", "\"layuimini\",", "\"logoinfo\":", "\"menuinfo\":", "\"msg\":", "\"other\":", "\"page/welcom", "\"pid\"", "\"remark\"", "\"root\")", "\"sdsdsdsff\";", "\"select", "\"sort\"", "\"status\",columndefinit", "\"system_menu\")", "\"target\",columndefinit", "\"target\":", "\"time\"", "\"tinyint", "\"title\")", "\"title\":", "\"update_at\"", "\"{title:", "\"其它管理\",", "\"常规管理\",", "\"根目录\",", "\"清除服务端缓存成功\"", "\"组件管理\",", "\"首页\"", "\"首页\",", "\"首页\";", "#content", "#context", "#ddl", "#gener", "#servlet:", "#thymeleaf模板配置", "#严格执行h5标准", "#关掉原生icon图标", "#最大220个并发，可以达到不丢包（可以自己实测），默认为200。", "#本地开发环境配置中心", "#解决使用其他库的表时候，把小数点变成下划线，导致sql无法成功执行。", "#设置tomcat参数", "#这是由于物理命名策略引起的，大写字母变小写，加_下划线（hibernate5以上高版本）", "#连接超时，单位为毫秒，", "#配置日志文件参数", "#配置访问路径，默认为/", "#静态资源路径", "#项目端口", "$", "$child", "$child;", "$homeinfo,", "$logoinfo", "$logoinfo,", "$menuinfo", "$menuinfo,", "$menulist", "$menulist);", "$menulist){", "$menulist;", "$node", "$node;", "$node['child']", "$systeminit", "$thi", "$treelist", "$treelist;", "$treelist[]", "$v", "$v)", "$v;", "$v['pid'])", "&&", "&menutreelist{", "'#0c0c0c',", "'#0c0f13',", "'#192027',", "'#197971',", "'#1aa094',", "'#1e9fff',", "'#23262e',", "'#243346',", "'#28333e',", "'#2f4056',", "'#3b3f4b',", "'#565656',", "'#737373',", "'#e4e4e4',", "'#ffffff',", "''", "'/ruge", "'0'", "'1'", "'_self'", "'desc')", "'homeinfo'", "'href'", "'id',", "'image'", "'images/logo.png',", "'images/logo.png'}\");", "'layer',", "'layui", "'layuimini'],", "'logoinfo'", "'menuinfo'", "'miniadmin'],", "'page/welcom", "'rgb(191,", "'rg<PERSON>(107,", "'rgba(160,", "'ruge", "'title'", "'创建时间',", "'删除时间',", "'名称',", "'备注信息',", "'更新时间',", "'最终的提交信息'", "'父id',", "'状态(0:禁用,1:启用)',", "'菜单图标',", "'菜单排序',", "'链接',", "'链接打开方式',", "'首页',", "'首页',href:", "(", "(!empty($child))", "($menulist", "($pid", "()", "(`href`)", "(`id`),", "(`title`),", "(array)$v;", "(c", "(childredatalist", "(data)", "(m", "(menuvo", "(parent.getchild()", "(parent.getid().equals(child.getpid()))", "(pid.equals(parent.getpid()))", "(sysmenu", "(systemmenuent", "(var", ")", "*", "*/", "*indexcontroller)", "*systemmenu)", ",", ".......", ".html", "/*", "/**", "/**复合主键要用这个注解*/", "//", "///", "//logo字体颜色,", "//logo背景颜色,", "//tab选项卡选中颜色,", "//头部右侧下拉字体颜色,", "//头部右侧字体颜色,", "//头部右侧更多下拉列表字体色,", "//头部右侧更多下拉列表选中背景色,", "//头部右侧更多下拉颜色,", "//头部右侧背景色", "//头部右侧选中背景色,", "//头部右侧鼠标选中,", "//头部缩放按钮样式,", "//左侧菜单子菜单背景,", "//左侧菜单字体颜色,", "//左侧菜单更多下拉样式,", "//左侧菜单背景,", "//左侧菜单选中字体颜色,", "//左侧菜单选中背景,", "//监听提交", "//这里我只查询页面转态为启用,可自行定义和写", "//这里是常规写法不使用lombok", "//递归获取子菜单", "/index/main", "/static/**", "0", "0\"", "0)", "0,", "0.7)',", "04", "06", "0l));", "1", "1\")", "1\");//控制器路由,自行定义", "1)", "1,", "1.html');", "1.html?t=1\"", "1.html?t=1\";", "1.html?t=1',", "1.html页面中有", "10", "10000", "107,", "123456", "160,", "1800000", "187)',", "187,", "1永不超时", "2", "2.html?mpi=m", "20", "2020", "2021", "220", "22:10:57", "25", "2之间", "30000", "5421757630121636006l;", "60000", "8", "8&servertimezone=gmt%2b8", "8080", ":", ":=", "=", "=\"permission_security.system_menu\"", "=\"表\"", "==", "===", "=>", ">", ">buildmenuchild($v", ">buildmenuchild($v['id'],", ">buildmenuchild(0,", ">field('id,pid,title,icon,href,target')", ">get();", ">getmenulist();", ">id,", ">json($systeminit);", ">order('sort',", ">orderby('sort',", ">pid)", ">select();", ">select(['id','pid','title','icon','href','target'])", ">where('status',", "@author", "@column(nam", "@createdd", "@data", "@date", "@descript", "@dynamicinsert", "@dynamicupd", "@embedd", "@embeddedid", "@entiti", "@generatedvalue(strategi", "@getmapping(\"/menu\")", "@getter", "@jsoninclude(jsoninclude.include.non_null)", "@overrid", "@query(valu", "@requestmapping(\"login\")", "@resourc", "@restcontrol", "@servic", "@setter", "@tabl", "@table(nam", "[", "[]*menutreelist", "[];", "[]systemmenu", "[]systemmenu)", "[key]", "[required]", "[table(\"bee_system_menu\")]", "]", "],", "];", "_", "_,", "`create_at`", "`delete_at`", "`href`", "`icon`", "`id`", "`json:\"child\"`", "`json:\"create_at\";orm:\"auto_now;type(datetime)\"`", "`json:\"homeinfo\"`", "`json:\"href\"`", "`json:\"icon\"`", "`json:\"id\"`", "`json:\"image\"`", "`json:\"logoinfo\"`", "`json:\"menuinfo\"`", "`json:\"pid\"`", "`json:\"remark\"`", "`json:\"sort\"`", "`json:\"status\"`", "`json:\"target\"`", "`json:\"title\"`", "`pid`", "`remark`", "`sort`", "`status`", "`system_menu`", "`target`", "`title`", "`update_at`", "address", "admin',image:", "admin/page/welcome.html'}}\");", "api项目数据库访问自行百度,数据库结构参考文档就行，结构都类似。", "app/http/controllers/indexcontroller.php", "app/index/controller/index.php", "append(treelist,", "application:", "arraylist();", "arraylist<>());", "arraylist<>();", "asp.net", "auto", "auto:", "auto_incr", "auto_increment=250", "awesome图标库的完美支持。", "awesome图标选择插件", "b", "back", "base:", "beego.control", "bgcolorconfig", "bgcolordefault", "bgcolordefault:", "bgcolordefault：系统默认皮肤，从0开始。", "book\",", "bool", "boolean", "buildmenuchild($pid,", "buildmenuchild(pid", "c.data[\"json\"]", "<PERSON><PERSON>()", "cache:", "chain:", "charset=utf8", "checkurldefault:", "checkurldefault：是否判断url有效，默认开启。", "child", "child)", "child;", "childredatalist)", "childredatalist.count()", "class", "classpath:/templates/", "clearinfo是服务端清理缓存信息(clearinfo.clearurl：服务端清理缓存接口地址，为空则不请求;)", "<PERSON><PERSON>l", "clearurl:", "clone", "com.mysql.jdbc.driv", "com.zaxxer.hikari.hikaridatasourc", "comment", "comment='系统菜单表';", "commit:", "config", "config.js文件，请根据实际情况修改里面扩展的路径。", "connect", "connections:", "console.log(\"plugin", "content", "content:", "control", "controllers/indexcontroller.go", "controllers/indexcontroller.java", "core", "creat", "create_at;", "createat", "createat)", "createat;", "databas", "database:", "datasource:", "date", "datebookhikaricp", "db::name('system_menu')", "db::table('system_menu')", "ddl:", "debug", "default", "delete_at;", "deleteat)", "deleteat;", "driver", "e", "e.target.tagnam", "element", "enabled:", "encoding:", "engine=innodb", "entity/menuentity.java", "entity层", "extend", "f:/mylog/adminlog.log", "fa", "fals", "false,", "false;", "favicon:", "figure:", "file:", "final", "findallbystatusorderbysort(boolean", "findchildren(menuvo", "foreach", "form", "form.on('submit(savebtn)',", "form.render();", "form表单刷新，部分组件不显示的情况，请在js上加上form.render();", "func", "function", "function(e){", "generationtype.identity)", "get;", "getchild()", "getcreateat()", "getdeleteat()", "gethref()", "geticon()", "getid()", "getkey()", "getmenulist()", "getmenulist(){", "getpid()", "getremark()", "getsort()", "getstatus()", "getsysteminit()", "getsystemmenubystatusandsort(long", "gettarget()", "gettitle()", "gettreenodelistbynolockeddtoarray(systemmenuentities,", "gettreenodelistbynolockeddtoarray(systemmenuentity[]", "getupdateat()", "gitbook文件修订时间：", "gitee下载命令：git", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/mast", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/onepag", "gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2", "gitee发版地址：https://gitee.com/zhongshaofa/layuimini/releas", "github下载命令：git", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/mast", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/onepag", "github仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2", "github发版地址：https://github.com/zhongshaofa/layuimini/releas", "golang动态生成初始化数据（beego框架）golang动态生成初始化数据（beego框架）", "golang示例(beego)", "hashmap<>(16);", "headerlogo:", "headerlogobg:", "headerlogocolor:", "headerright:", "headerrightbg:", "headerrightbgthis:", "headerrightchildcolor:", "headerrightcolor:", "headerrightcolorthis:", "headerrightnavmore:", "headerrightnavmorebg:", "headerrightnavmorecolor:", "headerrightthis:", "headerrighttoolcolor:", "hibernate:", "hikari:", "home", "home\",", "home.put(\"href\",\"/page/welcom", "home.put(\"title\",\"首页\");", "homeinfo", "home首页", "href", "href)", "href:", "href:\"page/form.html\",", "href;", "href=：页面链接，data", "href：页面链接，data", "https://gitee.com/zhongshaofa/layuimini", "https://github.com/zhongshaofa/layuimini", "icon", "icon)", "icon:", "icon;", "icon：图标）", "id", "id)", "id:", "id;", "idl", "idle:", "id必须唯一，例如", "ifram", "iframe版", "iframe窗口动画", "iis环境下请配置支持解析.json格式文件", "imag", "implement", "import", "index", "indexcontrol", "iniurl", "iniurl:", "insert和update注解,解决新增和更新的时候没有使用默认值", "int", "int(11)", "int,", "integ", "interfac", "item", "item);", "item.href,", "item.icon,", "item.id,", "item.title,", "java动态生成初始化数据（spring框架）java动态生成初始化数据（spring框架）", "java示例(spring)", "*****************************************************************", "jpa:", "jparepositori", "json($systeminit);", "js模块的拆分，代码更加规范化。", "key", "key)", "key;", "laravel框架示例(6.2版本)laravel框架示例(6.2版本)", "layer", "layer.alert(json.stringify(data.field),", "layer.close(index);", "layui.config({", "layui.element,", "layui.form,", "layui.j<PERSON>y,", "layui.layer,", "layui.layer;", "layui.layuimini;", "layui.miniadmin;", "layui.minipage;", "layui.minitab;", "layui.use(['element',", "layui.use(['form','layuimini'],", "layui.use(['form','minipage'],", "layui.use(['form','minitab'],", "layui.use(['jquery',", "<PERSON><PERSON><PERSON>", "layuimini.closecurrenttab();", "layuimini.hash('page/welcom", "layuimini.init('api/init.json');", "layuimini.init();", "layuimini.refresh();", "<PERSON><PERSON><PERSON>:", "layuimini后台模板项目介绍主要特性代码仓库(ifram", "leftmenubg:", "leftmen<PERSON>gthis:", "leftmenuchildbg:", "leftmenucolor:", "left<PERSON><PERSON><PERSON><PERSON><PERSON>:", "leftmenunavmore:", "legacyhtml5", "lemon", "len(child)", "level:", "lifetime:", "list", "list();", "loadingtim", "loadingtime:", "logging:", "logincontrol", "logo", "logo.put(\"image\",\"/static/images/back.jpg\");//静态资源文件路径,可使用默认的logo.png", "logo.put(\"title\",\"后台管理系统\");", "logoinfo", "long", "m.<PERSON>(0,", "m.get<PERSON>st()", "map", "map.put(\"homeinfo\",", "map.put(\"logoinfo\",", "map.put(\"menuinfo\",", "map;", "master", "max", "maxtab<PERSON>", "menu()", "menuchildopen", "menuchildopen:", "menuinfo", "menuinfo.add(menuvo);", "menuinfo.currency、menuinfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。", "menukey", "menuleft:", "menulefthover:", "menuleftthis:", "menulist", "menulist)", "menumodul", "menusinforesultdto", "menusinforesultdto.menuinfo", "menutreelist", "menuvo", "menuvo();", "menuvo.sethref(e.getkey().gethref());", "menuvo.seticon(e.geticon());", "menuvo.setid(e.getkey().getid());", "menuvo.setpid(e.getpid());", "menuvo.settarget(e.gettarget());", "menuvo.settitle(e.getkey().gettitle());", "min", "mini\"", "mini\",", "mini',", "miniadmin", "miniadmin.render(options);", "minimum", "minipag", "minipage.hashhome();", "minitab", "minitab.deletecurrentbyiframe();", "minitab.listen();", "minitab.opennewtabbyiframe({", "mode:", "model", "models/systemmenu.go", "module/layuimini/minitheme.js文件内添加", "multimodul", "multimodule:", "mvc:", "mysql", "name", "name:", "naming:", "new", "new(models.systemmenu).getsysteminit()", "node", "node)", "node.child", "null", "null)", "o", "o\",", "o.querytable(m.tablename()).filter(\"status\",1).orderby(\"", "onepag", "option", "order", "org.hibernate.boot.model.naming.physicalnamingstrategystandardimpl", "org.hibernate.dialect.mysql5dialect", "org:", "orm.new<PERSON>()", "p", "p.pid", "packag", "<PERSON><PERSON>m", "pageanim:", "parent", "parent,", "parent.getchild().add(findchildren(child,", "parent.setchild(new", "parent;", "password.html,us", "password.html页面中", "password:", "path", "path:", "paths:", "pattern:", "permission_security.system_menu", "php示例(laravel)", "php示例(thinkphp)", "physic", "pid", "pid)", "pid:", "pid;", "platform:", "pool", "popup....\");document.onclick", "port:", "prefix:", "primari", "privat", "public", "rang", "remark", "remark)", "remark;", "renderpagevers", "reposiroty/sysmenurepository.java", "repository层", "reserved，pow", "resource/application.yml", "resources:", "response()", "retlist", "retlist.add(findchildren(parent,", "retlist;", "return", "right", "root", "rootnod", "rootnode)", "rootnode.child", "rootnode.child)", "rootnode.child.add(treenode);", "rootnode.id);", "row_format=compact", "schema", "schema=\"\"可删掉不必跟我，前提是你的表对应你的表空间就是数据库", "serializ", "serialversionuid", "server:", "service/sysloginserviceimpl.java", "servlet:", "set;", "setchild(list", "setcreateat(d", "setdeleteat(d", "sethref(str", "seticon(str", "setid(long", "setkey(menukey", "setpid(long", "setremark(str", "setsort(long", "setstatus(integ", "settarget(str", "setting.html页面中", "setting.html页面中都有", "settitle(str", "setupdateat(d", "show", "slideshare\",", "sort", "sort\").all(&menulist)", "sort)", "sort);", "sort;", "spare", "spring:", "springboot", "springframework:", "sql:", "static", "statu", "status)", "status);", "status,integ", "status;", "strategy:", "string", "struct", "suffix:", "sysmenu", "sys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sysmenurepository.findallbystatusorderbysort(true);", "sysmenurepository;", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sysmenuservice.menu();", "sysmenuservice;", "sysmenuserviceimpl", "systeminit", "systeminit()", "systeminit.homeinfo.href", "systeminit.homeinfo.titl", "systeminit.logoinfo.imag", "systeminit.logoinfo.titl", "systeminit.menuinfo", "systemmenu", "systemmenu()", "systemmenuent", "systemmenuentities,", "systemmenuentities.count()", "tabactive:", "tabactivecolor:", "tabl", "tablename(\"system_menu\")", "tablename()", "tab选项卡进行重构，视觉和操作体验上更加良好。", "tab：页面链接，data", "target", "target)", "target:", "target;", "text", "text/html", "thinkphp框架示例(6.0版本)thinkphp框架示例(6.0版本)", "this.child", "this.createat", "this.createat;", "this.deleteat", "this.deleteat;", "this.href", "this.icon", "this.icon;", "this.id", "this.id;", "this.key", "this.pid", "this.pid;", "this.remark", "this.remark;", "this.sort", "this.sort;", "this.statu", "this.status;", "this.target", "this.target;", "this.titl", "this.updateat", "this.updateat;", "threads:", "thymeleaf:", "time.tim", "timeout:", "timestamp", "tinyint(1)", "titl", "title)", "title:", "title:\"按钮示例\",", "title;", "title：标题）", "title：标题，data", "todo", "tomcat:", "totree(list", "treelist", "treelist)", "treelist));", "treelist,", "treenod", "treeutil", "treeutil.totree(menuinfo,", "treeutilutil/treeutil.java", "true", "true)", "true,", "type", "type:", "unsign", "updat", "update_at;", "updateat)", "updateat;", "uri", "url:", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urlhashlocation:", "urlhashlocation：是否开启url地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页", "urlsuffixdefault:", "urlsuffixdefault：是否开启url后缀，默认开启。", "url后缀", "url地址hash定位", "url地址hash定位，可以清楚看到当前tab的地址信息。", "username:", "utf", "v", "v.buildmenuchild(v.id,", "v.href,", "v.icon,", "v.id,", "v.pid", "v.pid,", "v.target,", "v.title,", "v1版", "v1版本）", "v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中弹出新的tab窗口在iframe页面中关闭当前tab窗口后台主题方案配色常见问题备注信息使用说明（ifram", "v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中打开新页面在js中跳转页面在js中局部刷新页面后台主题方案配色常见问题备注信息使用说明（单页面", "v2", "v2版", "v2版本相比于v1，核心代码进行重构，更加更加规范，配置使用起来也更方便。", "v2版本）", "v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中弹出新的tab窗口（标签）在页面中弹出新的tab窗口（js方法）在iframe页面中关闭当前tab窗口后台主题方案配色常见问题备注信息使用说明（ifram", "v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中打开新的页面在内容页面中返回主页后台主题方案配色常见问题备注信息使用说明（单页面", "var", "<PERSON><PERSON><PERSON>(100)", "<PERSON><PERSON><PERSON>(20)", "<PERSON><PERSON><PERSON>(255)", "version:", "void", "web", "webadmin", "webapi接口示例", "webapi接口示例asp.net", "webapi示例", "window.open(e.target.src,e.target.src)}img{cursor:pointer}", "wupeng", "yml配置文件", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{", "||", "}", "}).extend({", "}).use(['layuimini'],", "});", "}*/", "},", "};", "©", "一个接口几行代码而已直接初始化整个框架，无需复杂操作。", "下载方式", "个人建议关闭", "个人建议开启", "为了方便演示，直接用db类去写", "主要特性", "主题默认配置", "代码仓库(ifram", "代码仓库(onepag", "以下参数是miniadmin.render();初始化时进行传入。", "优化主题配色方案", "优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。", "优化手机端初始化时的自适应，不会出现闪动的问题。", "优化返回主页按钮以及页面导航栏的实现", "使用说明（ifram", "使用说明（单页面", "修改js后刷新页面未生效，请尝试清除浏览器缓存。", "创建asp.net", "创建一个根对象来接受处理好的数据", "创建时间", "初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api地址返回的参数说明", "初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明", "初始化api接口返回的参数说明", "初始化加载时间", "初始化后台框架接口", "初始化接口", "初始化结构体", "初始化表单，要加上，不然刷新部分组件可能会不加载", "初始化页面是否加版本号", "删掉permission_security.", "删除时间", "刷新页面会保留当前的窗口，并且会定位当前窗口对应左侧菜单栏。", "加群请备注来源：如gitee、github、官网等。", "单页封装进行重构，视觉和操作体验上更加良好。", "单页版", "单页版)", "单页版)下载方式效果预览捐赠支持layuimini后台模板", "参数", "参数说明（data", "参数说明（href：页面链接，title：标题）", "参数说明（<PERSON>uimini", "发行版地址", "名称", "后台主题方案配色", "后台模板初始化", "后续此处加上用户的权限判断", "后面php、go的动态生成示例都是基于该表结构", "在iframe页面中关闭当前tab窗口", "在index.html文件内进行初始化", "在js中局部刷新页面", "在js中跳转页面", "在内容页面中返回主页", "在线预览地址：http://layuimini.99php.cn/iframe/v1/index.html", "在线预览地址：http://layuimini.99php.cn/iframe/v2/index.html", "在线预览地址：http://layuimini.99php.cn/onepage/v1/index.html", "在线预览地址：http://layuimini.99php.cn/onepage/v2/index.html", "在页面中弹出新的tab窗口", "在页面中弹出新的tab窗口（js方法）", "在页面中弹出新的tab窗口（标签）", "在页面中打开新的页面", "在页面中打开新的页面，请参考下方代码。（备注：需要引入minipage.js文件）", "在页面中打开新页面", "基本资料", "基础参数一览表", "备注", "备注信息", "复合主键要用这个注解", "多tab版)", "多tab版)代码仓库(onepag", "失效以及报错菜单无法直接打开，并给出弹出层提示完美的线上用户体验。", "如需在iframe页面中，请参考下方代码。（备注：minitab.js文件）", "如需在iframe页面中，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在js局部刷新页面，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在js跳转页面，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在页面中弹出新的tab窗口，请参考下方代码。", "如需在页面中弹出新的tab窗口，请参考下方代码。（备注：需要引入layuimini.js文件）", "如需在页面中弹出新的tab窗口，请参考下方代码。（备注：需要引入minitab.js文件）", "如需添加更多主题信息，请在js/lay", "子集", "完整的后端示例地址：https://github.com/chenyi2006520/systemmenu", "实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json", "对应model", "对应service逻辑层", "对应控制器", "常见问题", "序号", "建议0", "开启后，会显示路由信息，刷新页面后将定位到当前页", "开启后，页面不会有缓存问题", "开源项目不易，若此项目能得到你的青睐，可以捐赠支持作者持续开发与维护，感谢所有支持开源的朋友。", "引入lay", "引入miniadmin模块，根据需要传入初始化参数，执行miniadmin.render(options);", "总体预览", "打开新的窗口", "技术交流qq群：1165301500、667813249&#x1f235;、561838086&#x1f235;", "捐赠支持", "排序", "支持font", "支持多tab，可以打开多窗口。", "支持无限级菜单和对font", "效果预览", "数据id", "数据对象如下：", "数据库结构示例", "数据库表结构示例数据库表结构示例", "新增tab选项卡的切换与菜单之间的联动功能。", "新增初始化加载层，更好的视觉体验", "新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。", "新增初始化页面是否加版本号", "新增菜单在初始化的时候可以展开子菜单。", "新开tab方式", "方法。", "方法一：添加class样式layuimini", "方法二：minipage.hashhome();，示例在us", "方法内的参数请填写动态api地址。（实际应用中，请以后端api接口方式去实现）", "是logo信息", "是否判断url有效", "是否开启多模块", "是否打开hash定位", "是否菜单", "是否默认展开菜单", "是头部模块和左侧菜单对应的信息", "是首页信息", "更新时间", "更新说明", "最后将rootnode的child", "最大的tab打开数量", "最简洁、清爽、易用的layui后台框架模板。", "权限菜单树", "树结构对象", "添加tab或者切换时的过渡动漫", "父id", "父级id", "状态(0:禁用,1:启用)", "界面足够简洁清爽，响应式且适配手机端。", "示例在page/welcom", "示例在user", "示例提供来源：a0~海阔天空", "示例提供来源：香喷喷的如歌", "示例说明", "简介", "类型", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgcolorconfig方法内按相同格式添加。", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在minitheme.bgcolorconfig方法内按相同格式添加。", "系统已内置12套主题配色，如果需要自定义皮肤配色，请在minitheme.config方法内按相同格式添加。", "缓存清理接口", "缓存清理接口返回的参数说明", "节点名称", "节点地址", "获取初始化数据", "获取菜单列表", "菜单", "菜单图标", "菜单图标样式", "菜单排序", "菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。", "菜单结构体", "菜单结果对象", "菜单表", "说明", "调用方法进行监听：minipage.listen();(备注：框架初始化时已经进行监听，一般情况下不需要再次操作)", "调用方法进行监听：minitab.listen();", "调用方法：layuimini.closecurrenttab();", "调用方法：layuimini.hash(href);", "调用方法：layuimini.refresh();", "调用方法：minitab.deletecurrentbyiframe();", "赋值返回给", "返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)", "返回给前端就行", "递归处理数据", "递归处理数据库返回的数据方法参考如下，", "配置项移出到外部的初始化配置里面。", "重构手机端左侧菜单，弹出菜单时不会挤压内容内面。", "链接", "链接打开方式", "防止打开太多的tab窗口导致页面卡死", "页面支持多配色方案，可自行选择喜欢的配色。", "项目介绍", "项目会不定时进行更新，建议star和watch一份。", "首页", "默认值", "默认皮肤（0开始）", "默认配置在layuimini.config方法内，请自行修改", "默认配置说明"], "pipeline": ["stop<PERSON>ordFilter", "stemmer"]}, "store": {"./": {"url": "./", "title": "简介", "keywords": "", "body": "layuimini后台模板项目介绍主要特性代码仓库(iframe 多tab版)代码仓库(onepage 单页版)下载方式效果预览捐赠支持layuimini后台模板\n项目介绍\n最简洁、清爽、易用的layui后台框架模板。\n项目会不定时进行更新，建议star和watch一份。\n技术交流QQ群：1165301500、667813249&#x1F235;、561838086&#x1F235; 加群请备注来源：如gitee、github、官网等。\n主要特性\n\n界面足够简洁清爽，响应式且适配手机端。\n一个接口几行代码而已直接初始化整个框架，无需复杂操作。\n页面支持多配色方案，可自行选择喜欢的配色。\n支持多tab，可以打开多窗口。\n支持无限级菜单和对font-awesome图标库的完美支持。\n失效以及报错菜单无法直接打开，并给出弹出层提示完美的线上用户体验。\nurl地址hash定位，可以清楚看到当前tab的地址信息。\n刷新页面会保留当前的窗口，并且会定位当前窗口对应左侧菜单栏。\n支持font-awesome图标选择插件\n\n代码仓库(iframe 多tab版)\nv2版\n\n在线预览地址：http://layuimini.99php.cn/iframe/v2/index.html\nGitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2\nGitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2\n\nv1版\n\n在线预览地址：http://layuimini.99php.cn/iframe/v1/index.html\nGitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/master\nGitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/master\n\n代码仓库(onepage 单页版)\nv2版\n\n在线预览地址：http://layuimini.99php.cn/onepage/v2/index.html\nGitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2-onepage\nGitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2-onepage\n\nv1版\n\n在线预览地址：http://layuimini.99php.cn/onepage/v1/index.html\nGitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/onepage\nGitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/onepage\n\n下载方式\niframe v2版\n\nGitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b v2\nGitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b v2\n\niframe v1版\n\nGitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b master\nGitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b master\n\n单页版 v2版\n\nGitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b v2-onepage\nGitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b v2-onepage\n\n单页版 v1版\n\nGitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b onepage\nGitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b onepage\n\n发行版地址\n\nGitHub发版地址：https://github.com/zhongshaofa/layuimini/releases\nGitee发版地址：https://gitee.com/zhongshaofa/layuimini/releases\n\n效果预览\n\n总体预览\n\nFigure: Image text\n捐赠支持\n开源项目不易，若此项目能得到你的青睐，可以捐赠支持作者持续开发与维护，感谢所有支持开源的朋友。\n \n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/sql.html": {"url": "init/sql.html", "title": "数据库结构示例", "keywords": "", "body": "数据库表结构示例数据库表结构示例\n\n后面PHP、GO的动态生成示例都是基于该表结构\n\nCREATE TABLE `system_menu` (\n  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',\n  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',\n  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',\n  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '菜单图标',\n  `href` varchar(100) NOT NULL DEFAULT '' COMMENT '链接',\n  `target` varchar(20) NOT NULL DEFAULT '_self' COMMENT '链接打开方式',\n  `sort` int(11) DEFAULT '0' COMMENT '菜单排序',\n  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',\n  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',\n  `create_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',\n  `update_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',\n  `delete_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',\n  PRIMARY KEY (`id`),\n  KEY `title` (`title`),\n  KEY `href` (`href`)\n) ENGINE=InnoDB AUTO_INCREMENT=250 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='系统菜单表';\n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/laravel.html": {"url": "init/laravel.html", "title": "PHP示例(Lara<PERSON>)", "keywords": "", "body": "Laravel框架示例(6.2版本)Laravel框架示例(6.2版本)\n\n为了方便演示，直接用DB类去写 App/Http/Controllers/IndexController.php\n\n '首页',\n            'href'  => 'page/welcome-1.html?t=1',\n        ];\n        $logoInfo = [\n            'title' => 'LAYUI MINI',\n            'image' => 'images/logo.png',\n        ];\n        $menuInfo = $this->getMenuList();\n        $systemInit = [\n            'homeInfo' => $homeInfo,\n            'logoInfo' => $logoInfo,\n            'menuInfo' => $menuInfo,\n        ];\n        return response()->json($systemInit);\n    }\n\n    // 获取菜单列表\n    private function getMenuList(){\n        $menuList = DB::table('system_menu')\n            ->select(['id','pid','title','icon','href','target'])\n            ->where('status', 1)\n            ->orderBy('sort', 'desc')\n            ->get();\n        $menuList = $this->buildMenuChild(0, $menuList);\n        return $menuList;\n    }\n\n    //递归获取子菜单\n    private function buildMenuChild($pid, $menuList){\n        $treeList = [];\n        foreach ($menuList as $v) {\n            if ($pid == $v->pid) {\n                $node = (array)$v;\n                $child = $this->buildMenuChild($v->id, $menuList);\n                if (!empty($child)) {\n                    $node['child'] = $child;\n                }\n                // todo 后续此处加上用户的权限判断\n                $treeList[] = $node;\n            }\n        }\n        return $treeList;\n    }\n}\n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/thinkphp.html": {"url": "init/thinkphp.html", "title": "PHP示例(ThinkPHP)", "keywords": "", "body": "ThinkPHP框架示例(6.0版本)ThinkPHP框架示例(6.0版本)\n\n为了方便演示，直接用Db类去写 app/index/controller/Index.php\n\n '首页',\n            'href'  => 'page/welcome-1.html?t=1',\n        ];\n        $logoInfo = [\n            'title' => 'LAYUI MINI',\n            'image' => 'images/logo.png',\n        ];\n        $menuInfo = $this->getMenuList();\n        $systemInit = [\n            'homeInfo' => $homeInfo,\n            'logoInfo' => $logoInfo,\n            'menuInfo' => $menuInfo,\n        ];\n        return json($systemInit);\n    }\n\n    // 获取菜单列表\n    private function getMenuList(){\n        $menuList = Db::name('system_menu')\n            ->field('id,pid,title,icon,href,target')\n            ->where('status', 1)\n            ->order('sort', 'desc')\n            ->select();\n        $menuList = $this->buildMenuChild(0, $menuList);\n        return $menuList;\n    }\n\n    //递归获取子菜单\n    private function buildMenuChild($pid, $menuList){\n        $treeList = [];\n        foreach ($menuList as $v) {\n            if ($pid == $v['pid']) {\n                $node = $v;\n                $child = $this->buildMenuChild($v['id'], $menuList);\n                if (!empty($child)) {\n                    $node['child'] = $child;\n                }\n                // todo 后续此处加上用户的权限判断\n                $treeList[] = $node;\n            }\n        }\n        return $treeList;\n    }\n}\n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/golang.html": {"url": "init/golang.html", "title": "Golang示例(beego)", "keywords": "", "body": "Golang动态生成初始化数据（Beego框架）Golang动态生成初始化数据（Beego框架）\n\n对应控制器 controllers/IndexController.go\n\npackage controllers\n\nimport (\n    \"BeegoAdmin/models\"\n    \"github.com/astaxie/beego\"\n)\n\ntype IndexController struct {\n    beego.Controller\n}\n\n// 初始化后台框架接口\nfunc (c *IndexController) SystemInit() {\n    systemInit := new(models.SystemMenu).GetSystemInit()\n    c.Data[\"json\"] = systemInit\n    c.ServeJSON()\n}\n\n\n对应Model models/SystemMenu.go\n\npackage models\n\nimport (\n    \"github.com/astaxie/beego/orm\"\n    \"time\"\n)\n\n// 菜单\ntype SystemMenu struct {\n    Id       int       `json:\"id\"`\n    Pid      int       `json:\"pid\"`\n    Title    string    `json:\"title\"`\n    Icon     string    `json:\"icon\"`\n    Href     string    `json:\"href\"`\n    Sort     string    `json:\"sort\"`\n    Target   string    `json:\"target\"`\n    Remark   string    `json:\"remark\"`\n    Status   int       `json:\"status\"`\n    CreateAt time.Time `json:\"create_at\";orm:\"auto_now;type(datetime)\"`\n}\n\nfunc (m *SystemMenu) TableName() string {\n    return TableName(\"system_menu\")\n}\n\n// 初始化结构体\ntype SystemInit struct {\n    HomeInfo struct {\n        Title string `json:\"title\"`\n        Href  string `json:\"href\"`\n    } `json:\"homeInfo\"`\n    LogoInfo struct {\n        Title string `json:\"title\"`\n        Image string `json:\"image\"`\n    } `json:\"logoInfo\"`\n    MenuInfo []*MenuTreeList `json:\"menuInfo\"`\n}\n\n// 菜单结构体\ntype MenuTreeList struct {\n    Id     int             `json:\"id\"`\n    Pid    int             `json:\"pid\"`\n    Title  string          `json:\"title\"`\n    Icon   string          `json:\"icon\"`\n    Href   string          `json:\"href\"`\n    Target string          `json:\"target\"`\n    Remark string          `json:\"remark\"`\n    Child  []*MenuTreeList `json:\"child\"`\n}\n\n// 获取初始化数据\nfunc (m *SystemMenu) GetSystemInit() SystemInit {\n    var systemInit SystemInit\n\n    // 首页\n    systemInit.HomeInfo.Title = \"首页\"\n    systemInit.HomeInfo.Href = \"page/welcome-1.html?t=1\"\n\n    // logo\n    systemInit.LogoInfo.Title = \"LAYUI MINI\"\n    systemInit.LogoInfo.Image = \"images/logo.png\"\n\n    // 菜单\n    systemInit.MenuInfo = m.GetMenuList()\n\n    return systemInit\n}\n\n// 获取菜单列表\nfunc (m *SystemMenu) GetMenuList() []*MenuTreeList {\n    o := orm.NewOrm()\n    var menuList []SystemMenu\n    _, _ = o.QueryTable(m.TableName()).Filter(\"status\",1).OrderBy(\"-sort\").All(&menuList)\n    return m.buildMenuChild(0, menuList)\n}\n\n//递归获取子菜单\nfunc (m *SystemMenu) buildMenuChild(pid int, menuList []SystemMenu) []*MenuTreeList {\n    var treeList []*MenuTreeList\n    for _, v := range menuList {\n        if pid == v.Pid {\n            node := &MenuTreeList{\n                Id:     v.Id,\n                Title:  v.Title,\n                Icon:   v.Icon,\n                Href:   v.Href,\n                Target: v.Target,\n                Pid:    v.Pid,\n            }\n            child := v.buildMenuChild(v.Id, menuList)\n            if len(child) != 0 {\n                node.Child = child\n            }\n            // todo 后续此处加上用户的权限判断\n            treeList = append(treeList, node)\n        }\n    }\n    return treeList\n}\n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/java.html": {"url": "init/java.html", "title": "Java示例(spring)", "keywords": "", "body": "java动态生成初始化数据（spring框架）java动态生成初始化数据（spring框架）\n示例提供来源：香喷喷的如歌\n\n对应控制器 controllers/IndexController.java\n\n\n@RestController\n@RequestMapping(\"login\")\npublic class LoginController {\n    @Resource\n    private SysMenuService sysMenuService;\n\n    @GetMapping(\"/menu\")\n    public Map menu() {\n        return sysMenuService.menu();\n    }\n}\n\n对应Service逻辑层  service/SysLoginServiceImpl.java\n\n@Service\npublic class SysMenuServiceImpl implements SysMenuService {\n    @Resource\n    private SysMenuRepository sysMenuRepository;\n    @Override\n    public Map menu() {\n        Map map = new HashMap<>(16);\n        Map home = new HashMap<>(16);\n        Map logo = new HashMap<>(16);\n        List menuList = sysMenuRepository.findAllByStatusOrderBySort(true);\n        List menuInfo = new ArrayList<>();\n        for (SysMenu e : menuList) {\n            MenuVo menuVO = new MenuVo();\n            menuVO.setId(e.getKey().getId());\n            menuVO.setPid(e.getPid());\n            menuVO.setHref(e.getKey().getHref());\n            menuVO.setTitle(e.getKey().getTitle());\n            menuVO.setIcon(e.getIcon());\n            menuVO.setTarget(e.getTarget());\n            menuInfo.add(menuVO);\n        }\n        map.put(\"menuInfo\", TreeUtil.toTree(menuInfo, 0L));\n       home.put(\"title\",\"首页\");\n        home.put(\"href\",\"/page/welcome-1\");//控制器路由,自行定义\n        logo.put(\"title\",\"后台管理系统\");\n        logo.put(\"image\",\"/static/images/back.jpg\");//静态资源文件路径,可使用默认的logo.png \n        map.put(\"homeInfo\", \"{title: '首页',href: '/ruge-web-admin/page/welcome.html'}}\");\n        map.put(\"logoInfo\", \"{title: 'RUGE ADMIN',image: 'images/logo.png'}\");\n        return map;\n    }\n}\n\nTreeUtilutil/TreeUtil.java\n\npublic class TreeUtil {\n\n    public static List toTree(List treeList, Long pid) {\n        List retList = new ArrayList();\n        for (MenuVo parent : treeList) {\n            if (pid.equals(parent.getPid())) {\n                retList.add(findChildren(parent, treeList));\n            }\n        }\n        return retList;\n    }\n    private static MenuVo findChildren(MenuVo parent, List treeList) {\n        for (MenuVo child : treeList) {\n            if (parent.getId().equals(child.getPid())) {\n                if (parent.getChild() == null) {\n                    parent.setChild(new ArrayList<>());\n                }\n                parent.getChild().add(findChildren(child, treeList));\n            }\n        }\n        return parent;\n    }\n}\n\nrepository层 reposiroty/SysMenuRepository.java\n\npublic interface SysMenuRepository extends JpaRepository {\n\n    //这里我只查询页面转态为启用,可自行定义和写\n    @Query(value = \"select * from  permission_security.system_menu where STATUS = 1  ORDER BY  sort \",nativeQuery = true)\n    public List getSystemMenuByStatusAndSort(Long status,Integer sort);\n\n    List findAllByStatusOrderBySort(Boolean status);\n}\n\nentity层 entity/MenuEntity.java\n\n@Getter\n@Setter\n@Embeddable\npublic class MenuKey implements Serializable {\n    private Long id;\n    private String title;\n    private String href;\n}\n\n\n\n@Getter\n@Setter\n@Entity\n@Table(name = \"system_menu\")\npublic class SysMenu implements Serializable {\n  // 复合主键要用这个注解\n    @EmbeddedId\n    private MenuKey key;\n    private Long pid;\n    private String icon;\n    private String target;\n    private Integer sort;\n    private Boolean status;\n    private String remark;\n     @CreatedDate\n    private Date create_at;\n     @CreatedDate\n    private Date update_at;\n    private Date delete_at;\n}\n\n\n@Data\n@JsonInclude(JsonInclude.Include.NON_NULL)\npublic class MenuVo {\n    private Long id;\n\n    private Long pid;\n\n    private String title;\n\n    private String icon;\n\n    private String href;\n\n    private String target;\n\n    private List child;\n}\n\n//这里是常规写法不使用lombok\n\n/**\n *  insert和update注解,解决新增和更新的时候没有使用默认值\n * <AUTHOR> \n * @Date 2020-04-25\n * @Description   name =\"表\" 删掉permission_security. schema=\"\"可删掉不必跟我，前提是你的表对应你的表空间就是数据库\n */\n\n@Entity\n@Table ( name =\"permission_security.system_menu\" , schema = \"root\")\n@DynamicInsert\n@DynamicUpdate\npublic class SystemMenu  implements Serializable {\n\n    private static final long serialVersionUID =  5421757630121636006L;\n\n    /**复合主键要用这个注解*/\n    @EmbeddedId\n    private MenuKey key;\n\n\n    /**\n     * 父ID\n     */\n       @Column(name = \"pid\" )\n    private Long pid;\n\n\n\n    /**\n     * 菜单图标\n     */\n       @Column(name = \"icon\")\n    private String icon;\n\n    /**\n     * 链接打开方式\n     */\n       @Column(name = \"target\",columnDefinition = \"_self\")\n    private String target;\n    /**\n     * 菜单排序\n     */\n       @Column(name = \"sort\" )\n    private Long sort;\n\n    /**\n     * 状态(0:禁用,1:启用)\n     */\n       @Column(name = \"status\",columnDefinition = \"tinyint DEFAULT 1\")\n    private Integer status;\n\n    /**\n     * 备注信息\n     */\n       @Column(name = \"remark\" )\n    private String remark;\n\n    /**\n     * 创建时间\n     */\n       @Column(name = \"create_at\" )\n    private Date createAt;\n\n    /**\n     * 更新时间\n     */\n       @Column(name = \"update_at\" )\n    private Date updateAt;\n\n    /**\n     * 删除时间\n     */\n       @Column(name = \"delete_at\" )\n    private Date deleteAt;\n\n/*    public Long getId() {\n        return this.id;\n    }\n\n    public void setId(Long id) {\n        this.id = id;\n    }*/\n\n    public Long getPid() {\n        return this.pid;\n    }\n\n    public void setPid(Long pid) {\n        this.pid = pid;\n    }\n\n\n\n    public String getIcon() {\n        return this.icon;\n    }\n\n    public void setIcon(String icon) {\n        this.icon = icon;\n    }\n\n\n\n    public String getTarget() {\n        return this.target;\n    }\n\n    public void setTarget(String target) {\n        this.target = target;\n    }\n\n    public Long getSort() {\n        return this.sort;\n    }\n\n    public void setSort(Long sort) {\n        this.sort = sort;\n    }\n\n    public Integer getStatus() {\n        return this.status;\n    }\n\n    public void setStatus(Integer status) {\n        this.status = status;\n    }\n\n    public String getRemark() {\n        return this.remark;\n    }\n\n    public void setRemark(String remark) {\n        this.remark = remark;\n    }\n\n    public Date getCreateAt() {\n        return this.createAt;\n    }\n\n    public void setCreateAt(Date createAt) {\n        this.createAt = createAt;\n    }\n\n    public Date getUpdateAt() {\n        return this.updateAt;\n    }\n\n    public void setUpdateAt(Date updateAt) {\n        this.updateAt = updateAt;\n    }\n\n    public Date getDeleteAt() {\n        return this.deleteAt;\n    }\n\n    public void setDeleteAt(Date deleteAt) {\n        this.deleteAt = deleteAt;\n    }\n\n    public MenuKey getKey() {\n        return key;\n    }\n\n    public void setKey(MenuKey key) {\n        this.key = key;\n    }\n\n}\n\n\n@Embeddable\npublic class MenuKey implements Serializable {\n\n\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\n    @Column(name = \"id\")\n    private Long id;\n    /**\n     * 名称\n     */\n    @Column(name = \"title\")\n    private String title;\n    /**\n     * 链接\n     */\n    @Column(name = \"href\")\n    private String href;\n\n    public Long getId() {\n        return id;\n    }\n\n    public void setId(Long id) {\n        this.id = id;\n    }\n\n    public String getTitle() {\n        return title;\n    }\n\n    public void setTitle(String title) {\n        this.title = title;\n    }\n\n    public String getHref() {\n        return href;\n    }\n\n    public void setHref(String href) {\n        this.href = href;\n    }\n}\n\n@JsonInclude(JsonInclude.Include.NON_NULL)\npublic class MenuVo {\n\n    private Long id;\n\n    private Long pid;\n\n    private String title;\n\n    private String icon;\n\n    private String href;\n\n    private String target;\n\n    public Long getId() {\n        return id;\n    }\n\n    public void setId(Long id) {\n        this.id = id;\n    }\n\n    public Long getPid() {\n        return pid;\n    }\n\n    public void setPid(Long pid) {\n        this.pid = pid;\n    }\n\n    public String getTitle() {\n        return title;\n    }\n\n    public void setTitle(String title) {\n        this.title = title;\n    }\n\n    public String getIcon() {\n        return icon;\n    }\n\n    public void setIcon(String icon) {\n        this.icon = icon;\n    }\n\n    public String getHref() {\n        return href;\n    }\n\n    public void setHref(String href) {\n        this.href = href;\n    }\n\n    public String getTarget() {\n        return target;\n    }\n\n    public void setTarget(String target) {\n        this.target = target;\n    }\n\n    public List getChild() {\n        return child;\n    }\n\n    public void setChild(List child) {\n        this.child = child;\n    }\n\n    private List child;\n}\n\nyml配置文件 resource/application.yml\n\n#本地开发环境配置中心\n\nspring:\n  application:\n    name: springboot-webAdmin\n  jpa:\n    show-sql: true\n    database: mysql\n    #generate-ddl: true\n    database-platform: org.hibernate.dialect.MySQL5Dialect\n    hibernate:\n      naming:\n          #解决使用其他库的表时候，把小数点变成下划线，导致sql无法成功执行。\n          #这是由于物理命名策略引起的，大写字母变小写，加_下划线（hibernate5以上高版本）\n       physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl\n      #ddl-auto: update\n    #ddl-auto: update\n  datasource:\n    url: ********************************************************************************************    username: root\n    password: 123456\n    driver-class-name: com.mysql.jdbc.Driver\n    type: com.zaxxer.hikari.HikariDataSource\n    hikari:\n      auto-commit: true\n      minimum-idle: 2\n      idle-timeout: 60000\n      connection-timeout: 30000\n      max-lifetime: 1800000\n      pool-name: DatebookHikariCP\n  #thymeleaf模板配置\n  thymeleaf:\n    cache: false\n    enabled: true\n    prefix: classpath:/templates/\n    suffix: .html\n    #严格执行H5标准\n    mode: LEGACYHTML5\n    encoding: UTF-8\n    servlet:\n      content-type: text/html\n  #content-type: text/html\n  resources:\n    chain:\n      strategy:\n        content:\n          enabled: true\n          paths: /**\n\n  #静态资源路径\n  mvc:\n    static-path-pattern: /static/**\n    #关掉原生icon图标\n    favicon:\n      enabled: false\n\n#项目端口\nserver:\n  port: 8080\n  #连接超时，单位为毫秒，-1永不超时\n  connection-timeout: 60000\n  #设置tomcat参数\n  tomcat:\n    uri-encoding: utf-8\n    max-connections: 10000\n    min-spare-threads: 10\n    #最大220个并发，可以达到不丢包（可以自己实测），默认为200。\n    max-threads: 220\n  #配置访问路径，默认为/\n  #servlet:\n    #context-path: /index/main\n\n\n\n#配置日志文件参数\nlogging:\n  file:\n    path: F:/myLog/adminLog.log\n  level:\n    org:\n      springframework: debug\n    hibernate: debug\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "init/netcore.html": {"url": "init/netcore.html", "title": "ASP.NET CORE WebApi示例", "keywords": "", "body": "ASP.NET CORE WebApi接口示例ASP.NET CORE WebApi接口示例\n示例提供来源：A0~海阔天空\n\n创建ASP.NET CORE API项目数据库访问自行百度,数据库结构参考文档就行，结构都类似。\n完整的后端示例地址：https://github.com/chenyi2006520/SystemMenu\n数据对象如下：\n\n   /// \n    /// 菜单表\n    /// \n    [Table(\"bee_system_menu\")]\n    public class SystemMenuEntity\n    {\n        /// \n        /// ID\n        /// \n        [Key]\n        [Required]\n        public long id { get; set; }\n\n        /// \n        /// 父级ID\n        /// \n        [Required]\n        public long pid { get; set; }\n        /// \n        /// 名称\n        /// \n        [Required]\n        public string title { get; set; }\n\n        /// \n        /// 菜单图标\n        /// \n        public string icon { get; set; }\n\n        /// \n        /// 链接\n        /// \n        public string href { get; set; }\n\n        /// \n        /// 链接\n        /// \n        public string target { get; set; }\n        /// \n        /// 序号\n        /// \n        public int sort { get; set; }\n\n        /// \n        /// 是否菜单\n        /// \n        public bool status { get; set; }\n    }\n\n   /// \n    /// 菜单结果对象\n    /// \n    public class MenusInfoResultDTO\n    {\n        /// \n        /// 权限菜单树\n        /// \n        public List MenuInfo { get; set; }\n\n        /// \n        /// logo\n        /// \n        public LogoInfo LogoInfo { get; set; }\n\n        /// \n        /// Home\n        /// \n        public HomeInfo HomeInfo { get; set; }\n    }\n\n    public class LogoInfo\n    {\n        public string title { get; set; } = \"sdsdsdsff\";\n        public string image { get; set; } = \"images/logo.png\";\n        public string href { get; set; } = \"\";\n    }\n\n    public class HomeInfo\n    {\n        public string title { get; set; } = \"首页\";\n        public string href { get; set; } = \"page/welcome-1.html?t=1\";\n\n    }\n\n    /// \n    /// 树结构对象\n    /// \n    public class SystemMenu\n    {\n        /// \n        /// 数据ID\n        /// \n        public long Id { get; set; }\n\n         /// \n        /// 父级ID\n        /// \n        public long PId { get; set; }\n\n        /// \n        /// 节点名称\n        /// \n        public string Title { get; set; }\n\n        /// \n        /// 节点地址\n        /// \n        public string Href { get; set; }\n\n        /// \n        /// 新开Tab方式\n        /// \n        public string Target { get; set; } = \"_self\";\n\n        /// \n        /// 菜单图标样式\n        /// \n        public string Icon { get; set; }\n\n        /// \n        /// 排序\n        /// \n        public int Sort { get; set; }\n\n        /// \n        /// 子集\n        /// \n        public List Child { get; set; }\n    }\n\n\n创建一个根对象来接受处理好的数据\n\n    SystemMenu rootNode = new SystemMenu()\n    {\n        Id = 0,\n        Icon = \"\",\n        Href = \"\",\n        Title = \"根目录\",\n    };\n\n\n递归处理数据库返回的数据方法参考如下，\n\n    /// \n    /// 递归处理数据\n    /// \n    /// \n    /// \n    public static void GetTreeNodeListByNoLockedDTOArray(SystemMenuEntity[] systemMenuEntities, SystemMenu rootNode)\n    {\n        if (systemMenuEntities == null || systemMenuEntities.Count()  p.pid == rootNode.Id);\n        if (childreDataList != null && childreDataList.Count() > 0)\n        {\n            rootNode.Child = new List();\n\n            foreach (var item in childreDataList)\n            {\n                SystemMenu treeNode = new SystemMenu()\n                {\n                    Id = item.id,\n                    Icon = item.icon,\n                    Href = item.href,\n                    Title = item.title,\n                };\n                rootNode.Child.Add(treeNode);\n            }\n\n            foreach (var item in rootNode.Child)\n            {\n                GetTreeNodeListByNoLockedDTOArray(systemMenuEntities, item);\n            }\n        }\n    }\n\n\n最后将rootNode的Child 赋值返回给 MenusInfoResultDTO.MenuInfo 返回给前端就行\n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "iframe-v2.html": {"url": "iframe-v2.html", "title": "iframe版", "keywords": "", "body": "使用说明（iframe v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中弹出新的Tab窗口（标签）在页面中弹出新的Tab窗口（JS方法）在iframe页面中关闭当前Tab窗口后台主题方案配色常见问题备注信息使用说明（iframe v2版本）\n更新说明\n\nV2版本相比于V1，核心代码进行重构，更加更加规范，配置使用起来也更方便。\n\n\njs模块的拆分，代码更加规范化。\n配置项移出到外部的初始化配置里面。\ntab选项卡进行重构，视觉和操作体验上更加良好。\n新增tab选项卡的切换与菜单之间的联动功能。\n新增菜单在初始化的时候可以展开子菜单。\n新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。\n优化手机端初始化时的自适应，不会出现闪动的问题。\n重构手机端左侧菜单，弹出菜单时不会挤压内容内面。\n优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。\n新增初始化加载层，更好的视觉体验\n优化主题配色方案\n\n基础参数一览表\n\n以下参数是miniAdmin.render();初始化时进行传入。\n\n\n\n\n参数\n说明\n类型\n默认值\n备注\n\n\n\n\niniUrl\n初始化接口\nstring\nnull\n实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json\n\n\nclearUrl\n缓存清理接口\nstring\nnull\n实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json\n\n\nurlHashLocation\n是否打开hash定位\nbool\nfalse\n开启后，会显示路由信息，刷新页面后将定位到当前页\n\n\nbgColorDefault\n主题默认配置\nint\n0\n如需添加更多主题信息，请在js/lay-module/layuimini/miniTheme.js文件内添加\n\n\nmultiModule\n是否开启多模块\nbool\nfalse\n个人建议开启\n\n\nmenuChildOpen\n是否默认展开菜单\nbool\nfalse\n个人建议关闭\n\n\nloadingTime\n初始化加载时间\n0\n0\n建议0-2之间\n\n\npageAnim\niframe窗口动画\nbool\nfalse\n添加tab或者切换时的过渡动漫\n\n\nmaxTabNum\n最大的tab打开数量\nint\n20\n防止打开太多的tab窗口导致页面卡死\n\n\n\n\n示例说明\n\n        var options = {\n            iniUrl: \"api/init.json\",    // 初始化接口\n            clearUrl: \"api/clear.json\", // 缓存清理接口\n            urlHashLocation: true,      // 是否打开hash定位\n            bgColorDefault: 0,          // 主题默认配置\n            multiModule: true,          // 是否开启多模块\n            menuChildOpen: false,       // 是否默认展开菜单\n            loadingTime: 0,             // 初始化加载时间\n            pageAnim: true,             // iframe窗口动画\n        };\n        miniAdmin.render(options);\n\n后台模板初始化\n\n在index.html文件内进行初始化\n引入lay-config.js文件，请根据实际情况修改里面扩展的路径。\n引入miniAdmin模块，根据需要传入初始化参数，执行miniAdmin.render(options); 方法。\n初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明\n\n\n示例说明\n\n    layui.use(['jquery', 'layer', 'miniAdmin'], function () {\n        var $ = layui.jquery,\n            layer = layui.layer,\n            miniAdmin = layui.miniAdmin;\n\n        var options = {\n            iniUrl: \"api/init.json\",    // 初始化接口\n            clearUrl: \"api/clear.json\", // 缓存清理接口\n            urlHashLocation: true,      // 是否打开hash定位\n            bgColorDefault: 0,          // 主题默认配置\n            multiModule: true,          // 是否开启多模块\n            menuChildOpen: false,       // 是否默认展开菜单\n        };\n        miniAdmin.render(options);\n\n    });\n\n初始化api接口返回的参数说明\n\nhomeInfo 是首页信息\nlogoInfo 是logo信息\nmenuInfo 是头部模块和左侧菜单对应的信息\n\n\n示例说明\n\n{\n  \"homeInfo\": {\n    \"title\": \"首页\",\n    \"href\": \"page/welcome-1.html?t=1\"\n  },\n  \"logoInfo\": {\n    \"title\": \"LAYUI MINI\",\n    \"image\": \"images/logo.png\",\n    \"href\": \"\"\n  },\n  \"menuInfo\": [\n    {\n      \"title\": \"常规管理\",\n      \"icon\": \"fa fa-address-book\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    },\n    {\n      \"title\": \"组件管理\",\n      \"icon\": \"fa fa-lemon-o\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    },\n    {\n      \"title\": \"其它管理\",\n      \"icon\": \"fa fa-slideshare\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    }\n  ]\n}\n\n缓存清理接口返回的参数说明\n\n  返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)\n\n   {\n     \"code\": 1,\n     \"msg\": \"清除服务端缓存成功\"\n   }\n\n在页面中弹出新的Tab窗口（标签）\n\n如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入miniTab.js文件）\n参数说明（layuimini-content-href：页面链接，data-title：标题）\n调用方法进行监听：miniTab.listen();\n示例在page/welcome-1.html页面中有\n\n\n示例说明\n\n    基本资料\n\n    layui.use(['form','miniTab'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            miniTab = layui.miniTab;\n\n        miniTab.listen();\n\n    });\n\n在页面中弹出新的Tab窗口（JS方法）\n\n如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入miniTab.js文件）\n参数说明（href：页面链接，title：标题）\n\n\n示例说明\n\n\n    layui.use(['form','miniTab'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            miniTab = layui.miniTab;\n\n        // 打开新的窗口 \n        miniTab.openNewTabByIframe({\n            href:\"page/form.html\",\n            title:\"按钮示例\",\n        });\n\n    });\n\n在iframe页面中关闭当前Tab窗口\n\n如需在iframe页面中，请参考下方代码。（备注：miniTab.js文件）\n调用方法：miniTab.deleteCurrentByIframe();\n示例在user-password.html,user-setting.html页面中都有\n\n\n示例说明\n\n    layui.use(['form','miniTab'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            miniTab = layui.miniTab;\n\n        //监听提交\n        form.on('submit(saveBtn)', function (data) {\n            var index = layer.alert(JSON.stringify(data.field), {\n                title: '最终的提交信息'\n            }, function () {\n                layer.close(index);\n                miniTab.deleteCurrentByIframe();\n            });\n            return false;\n        });\n\n    });\n\n后台主题方案配色\n系统已内置12套主题配色，如果需要自定义皮肤配色，请在miniTheme.config方法内按相同格式添加。\n\n示例说明\n\n    var bgColorConfig = [\n                {\n                    headerRightBg: '#ffffff',                           //头部右侧背景色\n                    headerRightBgThis: '#e4e4e4',                       //头部右侧选中背景色,\n                    headerRightColor: 'rgba(107, 107, 107, 0.7)',       //头部右侧字体颜色,\n                    headerRightChildColor: 'rgba(107, 107, 107, 0.7)',  //头部右侧下拉字体颜色,\n                    headerRightColorThis: '#565656',                    //头部右侧鼠标选中,\n                    headerRightNavMore: 'rgba(160, 160, 160, 0.7)',     //头部右侧更多下拉颜色,\n                    headerRightNavMoreBg: '#1E9FFF',                    //头部右侧更多下拉列表选中背景色,\n                    headerRightNavMoreColor: '#ffffff',                 //头部右侧更多下拉列表字体色,\n                    headerRightToolColor: '#565656',                    //头部缩放按钮样式,\n                    headerLogoBg: '#192027',                            //logo背景颜色,\n                    headerLogoColor: 'rgb(191, 187, 187)',              //logo字体颜色,\n                    leftMenuNavMore: 'rgb(191, 187, 187)',              //左侧菜单更多下拉样式,\n                    leftMenuBg: '#28333E',                              //左侧菜单背景,\n                    leftMenuBgThis: '#1E9FFF',                          //左侧菜单选中背景,\n                    leftMenuChildBg: '#0c0f13',                         //左侧菜单子菜单背景,\n                    leftMenuColor: 'rgb(191, 187, 187)',                //左侧菜单字体颜色,\n                    leftMenuColorThis: '#ffffff',                       //左侧菜单选中字体颜色,\n                    tabActiveColor: '#1e9fff',                          //tab选项卡选中颜色,\n                },\n    ];\n\n常见问题\n\n修改js后刷新页面未生效，请尝试清除浏览器缓存。\nIIS环境下请配置支持解析.json格式文件\n\n备注信息\n\n菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 \n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "onepage-v2.html": {"url": "onepage-v2.html", "title": "单页版", "keywords": "", "body": "使用说明（单页面 v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中打开新的页面在内容页面中返回主页后台主题方案配色常见问题备注信息使用说明（单页面 v2版本）\n更新说明\n\nV2版本相比于V1，核心代码进行重构，更加更加规范，配置使用起来也更方便。\n\n\njs模块的拆分，代码更加规范化。\n配置项移出到外部的初始化配置里面。\n单页封装进行重构，视觉和操作体验上更加良好。\n新增菜单在初始化的时候可以展开子菜单。\n新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。\n优化手机端初始化时的自适应，不会出现闪动的问题。\n重构手机端左侧菜单，弹出菜单时不会挤压内容内面。\n优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。\n新增初始化加载层，更好的视觉体验\n新增初始化页面是否加版本号\n优化返回主页按钮以及页面导航栏的实现\n优化主题配色方案\n\n基础参数一览表\n\n以下参数是miniAdmin.render();初始化时进行传入。\n\n\n\n\n参数\n说明\n类型\n默认值\n备注\n\n\n\n\niniUrl\n初始化接口\nstring\nnull\n实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json\n\n\nclearUrl\n缓存清理接口\nstring\nnull\n实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json\n\n\nrenderPageVersion\n初始化页面是否加版本号\nbool\nfalse\n开启后，页面不会有缓存问题\n\n\nbgColorDefault\n主题默认配置\nint\n0\n如需添加更多主题信息，请在js/lay-module/layuimini/miniTheme.js文件内添加\n\n\nmultiModule\n是否开启多模块\nbool\nfalse\n个人建议开启\n\n\nmenuChildOpen\n是否默认展开菜单\nbool\nfalse\n个人建议关闭\n\n\nloadingTime\n初始化加载时间\n0\n0\n建议0-2之间\n\n\n\n\n示例说明\n\n        var options = {\n            iniUrl: \"api/init.json\",    // 初始化接口\n            clearUrl: \"api/clear.json\", // 缓存清理接口\n            urlHashLocation: true,      // 是否打开hash定位\n            bgColorDefault: 0,          // 主题默认配置\n            multiModule: true,          // 是否开启多模块\n            menuChildOpen: false,       // 是否默认展开菜单\n            loadingTime: 0,             // 初始化加载时间\n        };\n        miniAdmin.render(options);\n\n后台模板初始化\n\n在index.html文件内进行初始化\n\n引入lay-config.js文件，请根据实际情况修改里面扩展的路径。\n\n引入miniAdmin模块，根据需要传入初始化参数，执行miniAdmin.render(options); 方法。\n\n初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明\n\n\n\n示例说明\n\n    layui.use(['jquery', 'layer', 'miniAdmin'], function () {\n        var $ = layui.jquery,\n            layer = layui.layer,\n            miniAdmin = layui.miniAdmin;\n\n        var options = {\n            iniUrl: \"api/init.json\",    // 初始化接口\n            clearUrl: \"api/clear.json\", // 缓存清理接口\n            urlHashLocation: true,      // 是否打开hash定位\n            bgColorDefault: 0,          // 主题默认配置\n            multiModule: true,          // 是否开启多模块\n            menuChildOpen: false,       // 是否默认展开菜单\n        };\n        miniAdmin.render(options);\n\n    });\n\n初始化api接口返回的参数说明\n\nhomeInfo 是首页信息\n\nlogoInfo 是logo信息\n\nmenuInfo 是头部模块和左侧菜单对应的信息\n\n\n\n示例说明\n\n{\n  \"homeInfo\": {\n    \"title\": \"首页\",\n    \"href\": \"page/welcome-1.html?t=1\"\n  },\n  \"logoInfo\": {\n    \"title\": \"LAYUI MINI\",\n    \"image\": \"images/logo.png\",\n    \"href\": \"\"\n  },\n  \"menuInfo\": [\n    {\n      \"title\": \"常规管理\",\n      \"icon\": \"fa fa-address-book\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    },\n    {\n      \"title\": \"组件管理\",\n      \"icon\": \"fa fa-lemon-o\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    },\n    {\n      \"title\": \"其它管理\",\n      \"icon\": \"fa fa-slideshare\",\n      \"href\": \"\",\n      \"target\": \"_self\",\n      \"child\":[...]\n    }\n  ]\n}\n\n缓存清理接口返回的参数说明\n\n  返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)\n\n   {\n     \"code\": 1,\n     \"msg\": \"清除服务端缓存成功\"\n   }\n\n在页面中打开新的页面\n\n在页面中打开新的页面，请参考下方代码。（备注：需要引入miniPage.js文件）\n参数说明（layuimini-content-href=：页面链接，data-title：标题）\n调用方法进行监听：miniPage.listen();(备注：框架初始化时已经进行监听，一般情况下不需要再次操作)\n示例在page/welcome-1.html页面中有\n\n\n    基本资料\n\n在内容页面中返回主页\n\n方法一：添加class样式layuimini-back-home首页\n方法二：miniPage.hashHome();，示例在user-password.html,user-setting.html页面中都有\n\n\n示例说明\n\n    layui.use(['form','miniPage'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            miniPage = layui.miniPage;\n\n        /**\n         * 初始化表单，要加上，不然刷新部分组件可能会不加载\n         */\n        form.render();\n\n        //监听提交\n        form.on('submit(saveBtn)', function (data) {\n            var index = layer.alert(JSON.stringify(data.field), {\n                title: '最终的提交信息'\n            }, function () {\n                layer.close(index);\n                miniPage.hashHome();\n            });\n            return false;\n        });\n\n    });\n\n后台主题方案配色\n\n系统已内置12套主题配色，如果需要自定义皮肤配色，请在miniTheme.bgColorConfig方法内按相同格式添加。\n\n\n示例说明\n\n    var bgColorConfig = [\n                {\n                    headerRight: '#1aa094',\n                    headerRightThis: '#197971',\n                    headerLogo: '#243346',\n                    menuLeft: '#2f4056',\n                    menuLeftThis: '#1aa094',\n                    menuLeftHover: '#3b3f4b',\n                    tabActive: '#1aa094',\n                },\n                {\n                    headerRight: '#23262e',\n                    headerRightThis: '#0c0c0c',\n                    headerLogo: '#0c0c0c',\n                    menuLeft: '#23262e',\n                    menuLeftThis: '#737373',\n                    menuLeftHover: '#3b3f4b',\n                    tabActive: '#23262e',\n                }\n    ];\n\n常见问题\n\n修改js后刷新页面未生效，请尝试清除浏览器缓存。\nIIS环境下请配置支持解析.json格式文件\n\n备注信息\n\n菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 \n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "iframe.html": {"url": "iframe.html", "title": "iframe版", "keywords": "", "body": "使用说明（iframe v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中弹出新的Tab窗口在iframe页面中关闭当前Tab窗口后台主题方案配色常见问题备注信息使用说明（iframe v1版本）\n默认配置说明\n\n默认配置在layuimini.config方法内，请自行修改\nurlHashLocation：是否开启URL地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页\nurlSuffixDefault：是否开启URL后缀，默认开启。\nBgColorDefault：系统默认皮肤，从0开始。\ncheckUrlDefault：是否判断URL有效，默认开启。\n\n\n示例说明\n\n       var config = {\n             urlHashLocation: true,  // URL地址hash定位\n             urlSuffixDefault: true, // URL后缀\n             BgColorDefault: 0,      // 默认皮肤（0开始）\n             checkUrlDefault: true,  // 是否判断URL有效\n          };\n\n后台模板初始化\n\n在index.html文件内进行初始化\n\n引入lay-config.js文件，请根据实际情况修改里面扩展的路径。\n\nlayuimini.init(); 方法内的参数请填写动态api地址。（实际应用中，请以后端API接口方式去实现）\n\n初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明\n\n\n\n示例说明\n\n    layui.use(['element', 'layer', 'layuimini'], function () {\n        var $ = layui.jquery,\n            element = layui.element,\n            layer = layui.layer;\n\n        layuimini.init('api/init.json');\n    });\n\n初始化api地址返回的参数说明\n\nclearInfo是服务端清理缓存信息(clearInfo.clearUrl：服务端清理缓存接口地址，为空则不请求;)\n\n\n示例说明\n\n  // 返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)\n\n  {\n    \"code\": 1,\n    \"msg\": \"清除服务端缓存成功\"\n  }\n\n\nhomeInfo 是首页信息\n\nlogoInfo 是logo信息\n\nmenuInfo 是头部模块和左侧菜单对应的信息\n\nmenuModule id必须唯一，例如 menuInfo.currency、menuInfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。\n\n\n\n示例说明\n\n{\n  \"homeInfo\": {\n    \"title\": \"首页\",\n    \"icon\": \"fa fa-home\",\n    \"href\": \"page/welcome-2.html?mpi=m-p-i-0\"\n  },\n  \"logoInfo\": {\n    \"title\": \"LayuiMini\",\n    \"image\": \"images/logo.png\",\n    \"href\": \"\"\n  },\n  \"clearInfo\": {\n    \"clearUrl\": \"api/clear.json\"\n  },\n  \"menuInfo\": {\n      \"currency\": {\n        \"title\": \"常规管理\",\n        \"icon\": \"fa fa-address-book\",\n        \"child\": [\n            .......\n        ],\n      \"other\": {\n        \"title\": \"其它管理\",\n        \"icon\": \"fa fa-slideshare\",\n        \"child\": [\n            .......\n        ]\n    }\n  }\n}\n\n在页面中弹出新的Tab窗口\n\n如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入layuimini.js文件）\n参数说明（data-iframe-tab：页面链接，data-title：标题，data-icon：图标）\n\n\n示例说明\n\n\n    基本资料\n\n    \n        layui.config({\n            base: \"js/\",\n            version: true\n        }).extend({\n            layuimini: \"layuimini\"\n        }).use(['layuimini'], function () {\n        });\n     \n\n在iframe页面中关闭当前Tab窗口\n\n如需在iframe页面中，请参考下方代码。（备注：需要引入layuimini.js文件）\n调用方法：layuimini.closeCurrentTab();\n示例在user-password.html,user-setting.html页面中都有\n\n\n示例说明\n\n    layui.use(['form','layuimini'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            layuimini = layui.layuimini;\n\n        //监听提交\n        form.on('submit(saveBtn)', function (data) {\n            var index = layer.alert(JSON.stringify(data.field), {\n                title: '最终的提交信息'\n            }, function () {\n                layer.close(index);\n                layuimini.closeCurrentTab();\n            });\n            return false;\n        });\n    });\n\n后台主题方案配色\n\n系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgColorConfig方法内按相同格式添加。\n\n\n示例说明\n\n    var bgColorConfig = [\n        {\n            headerRight: '#1aa094',\n            headerRightThis: '#197971',\n            headerLogo: '#243346',\n            menuLeft: '#2f4056',\n            menuLeftThis: '#1aa094',\n            menuLeftHover: '#3b3f4b',\n        },\n        {\n            headerRight: '#23262e',\n            headerRightThis: '#0c0c0c',\n            headerLogo: '#0c0c0c',\n            menuLeft: '#23262e',\n            menuLeftThis: '#1aa094',\n            menuLeftHover: '#3b3f4b',\n        }\n    ];\n\n常见问题\n\n修改js后刷新页面未生效，请尝试清除浏览器缓存。\nIIS环境下请配置支持解析.json格式文件\n\n备注信息\n\n菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 \n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}, "onepage.html": {"url": "onepage.html", "title": "单页版", "keywords": "", "body": "使用说明（单页面 v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中打开新页面在js中跳转页面在js中局部刷新页面后台主题方案配色常见问题备注信息使用说明（单页面 v1版本）\n默认配置说明\n\n默认配置在layuimini.config方法内，请自行修改\nurlHashLocation：是否开启URL地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页\nurlSuffixDefault：是否开启URL后缀，默认开启。\nBgColorDefault：系统默认皮肤，从0开始。\n\n\n示例说明\n\n       var config = {\n             urlHashLocation: true,   // URL地址hash定位\n             urlSuffixDefault: true, // URL后缀\n             BgColorDefault: 0       // 默认皮肤（0开始）\n          };\n\n后台模板初始化\n\n在index.html文件内进行初始化\n\n引入lay-config.js文件，请根据实际情况修改里面扩展的路径。\n\nlayuimini.init(); 方法内的参数请填写动态api地址。（实际应用中，请以后端API接口方式去实现）\n\n初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明\n\n\n\n示例说明\n\n    layui.use(['element', 'layer', 'layuimini'], function () {\n        var $ = layui.jquery,\n            element = layui.element,\n            layer = layui.layer;\n\n        layuimini.init('api/init.json');\n    });\n\n初始化api地址返回的参数说明\n\nclearInfo是服务端清理缓存信息(clearInfo.clearUrl：服务端清理缓存接口地址，为空则不请求;)\n\n\n示例说明\n\n  返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；)\n\n  {\n    \"code\": 1,\n    \"msg\": \"清除服务端缓存成功\"\n  }\n\n\nhomeInfo 是首页信息\n\nlogoInfo 是logo信息\n\nmenuInfo 是头部模块和左侧菜单对应的信息\n\nmenuModule id必须唯一，例如 menuInfo.currency、menuInfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。\n\n\n\n示例说明\n\n{\n  \"homeInfo\": {\n    \"title\": \"首页\",\n    \"icon\": \"fa fa-home\",\n    \"href\": \"page/welcome-2.html?mpi=m-p-i-0\"\n  },\n  \"logoInfo\": {\n    \"title\": \"LayuiMini\",\n    \"image\": \"images/logo.png\",\n    \"href\": \"\"\n  },\n  \"clearInfo\": {\n    \"clearUrl\": \"api/clear.json\"\n  },\n  \"menuInfo\": {\n      \"currency\": {\n        \"title\": \"常规管理\",\n        \"icon\": \"fa fa-address-book\",\n        \"child\": [\n            .......\n        ],\n      \"other\": {\n        \"title\": \"其它管理\",\n        \"icon\": \"fa fa-slideshare\",\n        \"child\": [\n            .......\n        ]\n    }\n  }\n}\n\n在页面中打开新页面\n\n如需在页面中弹出新的Tab窗口，请参考下方代码。\n参数说明（data-iframe-tab：页面链接，data-title：标题，data-icon：图标）\n\n\n示例说明\n\n    基本资料\n\n在js中跳转页面\n\n如需在js跳转页面，请参考下方代码。（备注：需要引入layuimini.js文件）\n调用方法：layuimini.hash(href);\n示例在user-setting.html页面中\n\n\n示例说明\n\n    layui.use(['form','layuimini'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            layuimini = layui.layuimini;\n\n        /**\n         * 初始化表单，要加上，不然刷新部分组件可能会不加载\n         */\n        form.render();\n\n        //监听提交\n        form.on('submit(saveBtn)', function (data) {\n            var index = layer.alert(JSON.stringify(data.field), {\n                title: '最终的提交信息'\n            }, function () {\n                layer.close(index);\n                layuimini.hash('page/welcome-1.html');\n            });\n            return false;\n        });\n\n    });\n\n在js中局部刷新页面\n\n如需在js局部刷新页面，请参考下方代码。（备注：需要引入layuimini.js文件）\n调用方法：layuimini.refresh();\n示例在user-password.html页面中\n\n\n示例说明\n\n    layui.use(['form','layuimini'], function () {\n        var form = layui.form,\n            layer = layui.layer,\n            layuimini = layui.layuimini;\n\n        /**\n         * 初始化表单，要加上，不然刷新部分组件可能会不加载\n         */\n        form.render();\n\n        //监听提交\n        form.on('submit(saveBtn)', function (data) {\n            var index = layer.alert(JSON.stringify(data.field), {\n                title: '最终的提交信息'\n            }, function () {\n                layer.close(index);\n                layuimini.refresh();\n            });\n            return false;\n        });\n\n    });\n\n后台主题方案配色\n\n系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgColorConfig方法内按相同格式添加。\n\n\n示例说明\n\n    var bgColorConfig = [\n        {\n            headerRight: '#1aa094',\n            headerRightThis: '#197971',\n            headerLogo: '#243346',\n            menuLeft: '#2f4056',\n            menuLeftThis: '#1aa094',\n            menuLeftHover: '#3b3f4b',\n        },\n        {\n            headerRight: '#23262e',\n            headerRightThis: '#0c0c0c',\n            headerLogo: '#0c0c0c',\n            menuLeft: '#23262e',\n            menuLeftThis: '#1aa094',\n            menuLeftHover: '#3b3f4b',\n        }\n    ];\n\n常见问题\n\nIIS环境下请配置支持解析.json格式文件\n修改js后刷新页面未生效，请尝试清除浏览器缓存。\nform表单刷新，部分组件不显示的情况，请在js上加上form.render();\n\n备注信息\n\n菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 \n\n© zhongshaofa all right reserved，powered by Gitbook文件修订时间：\n2021-04-06 22:10:57\n\nconsole.log(\"plugin-popup....\");document.onclick = function(e){ e.target.tagName === \"IMG\" && window.open(e.target.src,e.target.src)}img{cursor:pointer}"}}}