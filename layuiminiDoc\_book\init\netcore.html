
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>ASP.NET CORE WebApi示例 · layuimini开发手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="zhongshaofa">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-anchor-nav-x/style/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-tbfed-pagefooter/footer.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-image-captions/image-captions.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-code/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-insert-logo/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-donate/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    
    <link rel="prev" href="java.html" />
    

    <style>
    @media only screen and (max-width: 640px) {
        .book-header .hidden-mobile {
            display: none;
        }
    }
    </style>
    <script>
        window["gitbook-plugin-github-buttons"] = {"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]};
    </script>

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" >
            
                <span>
            
                    
                    初始化接口后端示例(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="sql.html">
            
                <a href="sql.html">
            
                    
                    数据库结构示例
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="laravel.html">
            
                <a href="laravel.html">
            
                    
                    PHP示例(Laravel)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="thinkphp.html">
            
                <a href="thinkphp.html">
            
                    
                    PHP示例(ThinkPHP)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.4" data-path="golang.html">
            
                <a href="golang.html">
            
                    
                    Golang示例(beego)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.5" data-path="java.html">
            
                <a href="java.html">
            
                    
                    Java示例(spring)
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="1.2.6" data-path="netcore.html">
            
                <a href="netcore.html">
            
                    
                    ASP.NET CORE WebApi示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" >
            
                <span>
            
                    
                    使用说明(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="*******" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.5" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.6" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在页面中弹出新的Tab窗口（标签）">
            
                    
                    在页面中弹出新的Tab窗口（标签）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.7" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在页面中弹出新的Tab窗口（JS方法）">
            
                    
                    在页面中弹出新的Tab窗口（JS方法）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.8" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.9" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******0" data-path="../iframe-v2.html">
            
                <a href="../iframe-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html">
            
                    
                    单页版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.3" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.4" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.5" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.6" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#在页面中打开新的页面">
            
                    
                    在页面中打开新的页面
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.7" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#在内容页面中返回主页">
            
                    
                    在内容页面中返回主页
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.8" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.9" data-path="../onepage-v2.html">
            
                <a href="../onepage-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" >
            
                <span>
            
                    
                    使用说明(V1版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../iframe.html">
            
                <a href="../iframe.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1.1" data-path="../iframe.html">
            
                <a href="../iframe.html#默认配置说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.2" data-path="../iframe.html">
            
                <a href="../iframe.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.3" data-path="../iframe.html">
            
                <a href="../iframe.html#初始化api地址返回的参数说明">
            
                    
                    初始化api地址返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.4" data-path="../iframe.html">
            
                <a href="../iframe.html#在页面中弹出新的Tab窗口">
            
                    
                    在页面中弹出新的Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.5" data-path="../iframe.html">
            
                <a href="../iframe.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.6" data-path="../iframe.html">
            
                <a href="../iframe.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.7" data-path="../iframe.html">
            
                <a href="../iframe.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../onepage.html">
            
                <a href="../onepage.html">
            
                    
                    单页版
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >ASP.NET CORE WebApi示例</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-minus-circle"></i><ul><li><span class="title-icon "></span><a href="#aspnet-core-webapi&#x63A5;&#x53E3;&#x793A;&#x4F8B;"><b></b>ASP.NET CORE WebApi&#x63A5;&#x53E3;&#x793A;&#x4F8B;</a></li></ul></div><a href="#aspnet-core-webapi&#x63A5;&#x53E3;&#x793A;&#x4F8B;" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><h1 id="aspnet-core-webapi&#x63A5;&#x53E3;&#x793A;&#x4F8B;"><a name="aspnet-core-webapi&#x63A5;&#x53E3;&#x793A;&#x4F8B;" class="anchor-navigation-ex-anchor" href="#aspnet-core-webapi&#x63A5;&#x53E3;&#x793A;&#x4F8B;"><i class="fa fa-link" aria-hidden="true"></i></a>ASP.NET CORE WebApi&#x63A5;&#x53E3;&#x793A;&#x4F8B;</h1>
<h5 id="&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;a0&#x6D77;&#x9614;&#x5929;&#x7A7A;"><a name="&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;a0&#x6D77;&#x9614;&#x5929;&#x7A7A;" class="anchor-navigation-ex-anchor" href="#&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;a0&#x6D77;&#x9614;&#x5929;&#x7A7A;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x793A;&#x4F8B;&#x63D0;&#x4F9B;&#x6765;&#x6E90;&#xFF1A;A0~&#x6D77;&#x9614;&#x5929;&#x7A7A;</h5>
<blockquote>
<p>&#x521B;&#x5EFA;ASP.NET CORE API&#x9879;&#x76EE;&#x6570;&#x636E;&#x5E93;&#x8BBF;&#x95EE;&#x81EA;&#x884C;&#x767E;&#x5EA6;,&#x6570;&#x636E;&#x5E93;&#x7ED3;&#x6784;&#x53C2;&#x8003;&#x6587;&#x6863;&#x5C31;&#x884C;&#xFF0C;&#x7ED3;&#x6784;&#x90FD;&#x7C7B;&#x4F3C;&#x3002;</p>
<p>&#x5B8C;&#x6574;&#x7684;&#x540E;&#x7AEF;&#x793A;&#x4F8B;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/chenyi2006520/SystemMenu" target="_blank">https://github.com/chenyi2006520/SystemMenu</a></p>
<p>&#x6570;&#x636E;&#x5BF9;&#x8C61;&#x5982;&#x4E0B;&#xFF1A;</p>
</blockquote>
<pre><code class="lang-C#">   /// &lt;summary&gt;
    /// &#x83DC;&#x5355;&#x8868;
    /// &lt;/summary&gt;
    [Table(&quot;bee_system_menu&quot;)]
    public class SystemMenuEntity
    {
        /// &lt;summary&gt;
        /// ID
        /// &lt;/summary&gt;
        [Key]
        [Required]
        public long id { get; set; }

        /// &lt;summary&gt;
        /// &#x7236;&#x7EA7;ID
        /// &lt;/summary&gt;
        [Required]
        public long pid { get; set; }
        /// &lt;summary&gt;
        /// &#x540D;&#x79F0;
        /// &lt;/summary&gt;
        [Required]
        public string title { get; set; }

        /// &lt;summary&gt;
        /// &#x83DC;&#x5355;&#x56FE;&#x6807;
        /// &lt;/summary&gt;
        public string icon { get; set; }

        /// &lt;summary&gt;
        /// &#x94FE;&#x63A5;
        /// &lt;/summary&gt;
        public string href { get; set; }

        /// &lt;summary&gt;
        /// &#x94FE;&#x63A5;
        /// &lt;/summary&gt;
        public string target { get; set; }
        /// &lt;summary&gt;
        /// &#x5E8F;&#x53F7;
        /// &lt;/summary&gt;
        public int sort { get; set; }

        /// &lt;summary&gt;
        /// &#x662F;&#x5426;&#x83DC;&#x5355;
        /// &lt;/summary&gt;
        public bool status { get; set; }
    }
</code></pre>
<pre><code class="lang-C#">   /// &lt;summary&gt;
    /// &#x83DC;&#x5355;&#x7ED3;&#x679C;&#x5BF9;&#x8C61;
    /// &lt;/summary&gt;
    public class MenusInfoResultDTO
    {
        /// &lt;summary&gt;
        /// &#x6743;&#x9650;&#x83DC;&#x5355;&#x6811;
        /// &lt;/summary&gt;
        public List&lt;SystemMenu&gt; MenuInfo { get; set; }

        /// &lt;summary&gt;
        /// logo
        /// &lt;/summary&gt;
        public LogoInfo LogoInfo { get; set; }

        /// &lt;summary&gt;
        /// Home
        /// &lt;/summary&gt;
        public HomeInfo HomeInfo { get; set; }
    }

    public class LogoInfo
    {
        public string title { get; set; } = &quot;sdsdsdsff&quot;;
        public string image { get; set; } = &quot;images/logo.png&quot;;
        public string href { get; set; } = &quot;&quot;;
    }

    public class HomeInfo
    {
        public string title { get; set; } = &quot;&#x9996;&#x9875;&quot;;
        public string href { get; set; } = &quot;page/welcome-1.html?t=1&quot;;

    }

    /// &lt;summary&gt;
    /// &#x6811;&#x7ED3;&#x6784;&#x5BF9;&#x8C61;
    /// &lt;/summary&gt;
    public class SystemMenu
    {
        /// &lt;summary&gt;
        /// &#x6570;&#x636E;ID
        /// &lt;/summary&gt;
        public long Id { get; set; }

         /// &lt;summary&gt;
        /// &#x7236;&#x7EA7;ID
        /// &lt;/summary&gt;
        public long PId { get; set; }

        /// &lt;summary&gt;
        /// &#x8282;&#x70B9;&#x540D;&#x79F0;
        /// &lt;/summary&gt;
        public string Title { get; set; }

        /// &lt;summary&gt;
        /// &#x8282;&#x70B9;&#x5730;&#x5740;
        /// &lt;/summary&gt;
        public string Href { get; set; }

        /// &lt;summary&gt;
        /// &#x65B0;&#x5F00;Tab&#x65B9;&#x5F0F;
        /// &lt;/summary&gt;
        public string Target { get; set; } = &quot;_self&quot;;

        /// &lt;summary&gt;
        /// &#x83DC;&#x5355;&#x56FE;&#x6807;&#x6837;&#x5F0F;
        /// &lt;/summary&gt;
        public string Icon { get; set; }

        /// &lt;summary&gt;
        /// &#x6392;&#x5E8F;
        /// &lt;/summary&gt;
        public int Sort { get; set; }

        /// &lt;summary&gt;
        /// &#x5B50;&#x96C6;
        /// &lt;/summary&gt;
        public List&lt;SystemMenu&gt; Child { get; set; }
    }
</code></pre>
<blockquote>
<p>&#x521B;&#x5EFA;&#x4E00;&#x4E2A;&#x6839;&#x5BF9;&#x8C61;&#x6765;&#x63A5;&#x53D7;&#x5904;&#x7406;&#x597D;&#x7684;&#x6570;&#x636E;</p>
</blockquote>
<pre><code class="lang-C#">    SystemMenu rootNode = new SystemMenu()
    {
        Id = 0,
        Icon = &quot;&quot;,
        Href = &quot;&quot;,
        Title = &quot;&#x6839;&#x76EE;&#x5F55;&quot;,
    };
</code></pre>
<blockquote>
<p>&#x9012;&#x5F52;&#x5904;&#x7406;&#x6570;&#x636E;&#x5E93;&#x8FD4;&#x56DE;&#x7684;&#x6570;&#x636E;&#x65B9;&#x6CD5;&#x53C2;&#x8003;&#x5982;&#x4E0B;&#xFF0C;</p>
</blockquote>
<pre><code class="lang-C#">    /// &lt;summary&gt;
    /// &#x9012;&#x5F52;&#x5904;&#x7406;&#x6570;&#x636E;
    /// &lt;/summary&gt;
    /// &lt;param name=&quot;systemMenuEntities&quot;&gt;&lt;/param&gt;
    /// &lt;param name=&quot;rootNode&quot;&gt;&lt;/param&gt;
    public static void GetTreeNodeListByNoLockedDTOArray(SystemMenuEntity[] systemMenuEntities, SystemMenu rootNode)
    {
        if (systemMenuEntities == null || systemMenuEntities.Count() &lt;= 0)
        {
            return;
        }

        var childreDataList = systemMenuEntities.Where(p =&gt; p.pid == rootNode.Id);
        if (childreDataList != null &amp;&amp; childreDataList.Count() &gt; 0)
        {
            rootNode.Child = new List&lt;SystemMenu&gt;();

            foreach (var item in childreDataList)
            {
                SystemMenu treeNode = new SystemMenu()
                {
                    Id = item.id,
                    Icon = item.icon,
                    Href = item.href,
                    Title = item.title,
                };
                rootNode.Child.Add(treeNode);
            }

            foreach (var item in rootNode.Child)
            {
                GetTreeNodeListByNoLockedDTOArray(systemMenuEntities, item);
            }
        }
    }
</code></pre>
<blockquote>
<p>&#x6700;&#x540E;&#x5C06;rootNode&#x7684;Child &#x8D4B;&#x503C;&#x8FD4;&#x56DE;&#x7ED9; MenusInfoResultDTO.MenuInfo &#x8FD4;&#x56DE;&#x7ED9;&#x524D;&#x7AEF;&#x5C31;&#x884C;</p>
</blockquote>
<footer class="page-footer"><span class="copyright">&#xA9; zhongshaofa all right reserved&#xFF0C;powered by Gitbook</span><span class="footer-modification">&#x6587;&#x4EF6;&#x4FEE;&#x8BA2;&#x65F6;&#x95F4;&#xFF1A;
2021-04-06 22:10:57
</span></footer>
<script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="java.html" class="navigation navigation-prev navigation-unique" aria-label="Previous page: Java示例(spring)">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"ASP.NET CORE WebApi示例","level":"1.2.6","depth":2,"next":{"title":"使用说明(V2版)","level":"1.3","depth":1,"ref":"","articles":[{"title":"iframe版","level":"1.3.1","depth":2,"path":"iframe-v2.md","ref":"iframe-v2.md","articles":[{"title":"更新说明","level":"*******","depth":3,"anchor":"#更新说明","path":"iframe-v2.md","ref":"iframe-v2.md#更新说明","articles":[]},{"title":"基础参数一览表","level":"*******","depth":3,"anchor":"#基础参数一览表","path":"iframe-v2.md","ref":"iframe-v2.md#基础参数一览表","articles":[]},{"title":"后台模板初始化","level":"*******","depth":3,"anchor":"#后台模板初始化","path":"iframe-v2.md","ref":"iframe-v2.md#后台模板初始化","articles":[]},{"title":"初始化api接口返回的参数说明","level":"*******","depth":3,"anchor":"#初始化api接口返回的参数说明","path":"iframe-v2.md","ref":"iframe-v2.md#初始化api接口返回的参数说明","articles":[]},{"title":"缓存清理接口返回的参数说明","level":"1.3.1.5","depth":3,"anchor":"#缓存清理接口返回的参数说明","path":"iframe-v2.md","ref":"iframe-v2.md#缓存清理接口返回的参数说明","articles":[]},{"title":"在页面中弹出新的Tab窗口（标签）","level":"1.3.1.6","depth":3,"anchor":"#在页面中弹出新的Tab窗口（标签）","path":"iframe-v2.md","ref":"iframe-v2.md#在页面中弹出新的Tab窗口（标签）","articles":[]},{"title":"在页面中弹出新的Tab窗口（JS方法）","level":"1.3.1.7","depth":3,"anchor":"#在页面中弹出新的Tab窗口（JS方法）","path":"iframe-v2.md","ref":"iframe-v2.md#在页面中弹出新的Tab窗口（JS方法）","articles":[]},{"title":"在iframe页面中关闭当前Tab窗口","level":"1.3.1.8","depth":3,"anchor":"#在iframe页面中关闭当前Tab窗口","path":"iframe-v2.md","ref":"iframe-v2.md#在iframe页面中关闭当前Tab窗口","articles":[]},{"title":"后台主题方案配色","level":"1.3.1.9","depth":3,"anchor":"#后台主题方案配色","path":"iframe-v2.md","ref":"iframe-v2.md#后台主题方案配色","articles":[]},{"title":"常见问题","level":"*******0","depth":3,"anchor":"#常见问题","path":"iframe-v2.md","ref":"iframe-v2.md#常见问题","articles":[]}]},{"title":"单页版","level":"1.3.2","depth":2,"path":"onepage-v2.md","ref":"onepage-v2.md","articles":[{"title":"更新说明","level":"1.3.2.1","depth":3,"anchor":"#更新说明","path":"onepage-v2.md","ref":"onepage-v2.md#更新说明","articles":[]},{"title":"基础参数一览表","level":"1.3.2.2","depth":3,"anchor":"#基础参数一览表","path":"onepage-v2.md","ref":"onepage-v2.md#基础参数一览表","articles":[]},{"title":"后台模板初始化","level":"1.3.2.3","depth":3,"anchor":"#后台模板初始化","path":"onepage-v2.md","ref":"onepage-v2.md#后台模板初始化","articles":[]},{"title":"初始化api接口返回的参数说明","level":"1.3.2.4","depth":3,"anchor":"#初始化api接口返回的参数说明","path":"onepage-v2.md","ref":"onepage-v2.md#初始化api接口返回的参数说明","articles":[]},{"title":"缓存清理接口返回的参数说明","level":"1.3.2.5","depth":3,"anchor":"#缓存清理接口返回的参数说明","path":"onepage-v2.md","ref":"onepage-v2.md#缓存清理接口返回的参数说明","articles":[]},{"title":"在页面中打开新的页面","level":"1.3.2.6","depth":3,"anchor":"#在页面中打开新的页面","path":"onepage-v2.md","ref":"onepage-v2.md#在页面中打开新的页面","articles":[]},{"title":"在内容页面中返回主页","level":"1.3.2.7","depth":3,"anchor":"#在内容页面中返回主页","path":"onepage-v2.md","ref":"onepage-v2.md#在内容页面中返回主页","articles":[]},{"title":"后台主题方案配色","level":"1.3.2.8","depth":3,"anchor":"#后台主题方案配色","path":"onepage-v2.md","ref":"onepage-v2.md#后台主题方案配色","articles":[]},{"title":"常见问题","level":"1.3.2.9","depth":3,"anchor":"#常见问题","path":"onepage-v2.md","ref":"onepage-v2.md#常见问题","articles":[]}]}]},"previous":{"title":"Java示例(spring)","level":"1.2.5","depth":2,"path":"init/java.md","ref":"init/java.md","articles":[]},"dir":"ltr"},"config":{"plugins":["anchor-nav-x","tbfed-pagefooter","search-plus","popup","highlight","image-captions","styled-blockquotes","expandable-chapters-interactive","hide-element","code","insert-logo","custom-favicon","github","github-buttons","donate","baidu-tongji"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"&copy zhongshaofa","modify_label":"文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"github":{"url":"https://github.com/zhongshaofa/layuimini"},"baidu-tongji":{"url":"https://hm.baidu.com/hm.js","token":"5637e63b6bd6247c36cf245293f121f9"},"search":{},"styled-blockquotes":{},"popup":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"donate":{"alipay":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/ali_pay.png","alipayText":"支付宝打赏","button":"打赏","title":"","wechat":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/wechat_pay.png","wechatText":"微信打赏"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"favicon":"images/favicon.ico","expandable-chapters-interactive":{},"anchor-nav-x":{"associatedWithSummary":true,"float":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showLevel":true},"github-buttons":{"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]},"custom-favicon":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"insert-logo":{"style":"background: none; max-height: 60px; min-height: 60px","url":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/logo_2.png"},"search-plus":{},"image-captions":{"variable_name":"_pictures"}},"theme":"default","author":"zhongshaofa","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[{"backlink":"index.html#fig1.1.1","level":"1.1","list_caption":"Figure: Image text","alt":"Image text","nro":1,"url":"./images/home.png","index":1,"caption_template":"Figure: _CAPTION_","label":"Image text","attributes":{},"skip":false,"key":"1.1.1"}]},"title":"layuimini开发手册","language":"zh-hans","gitbook":"*","sharing":{"qq":false,"all":["google","facebook","weibo","twitter","qq","qzone","linkedin","pocket"],"douban":false,"facebook":false,"weibo":false,"instapaper":false,"whatsapp":false,"hatenaBookmark":false,"twitter":false,"messenger":false,"line":false,"vk":false,"pocket":false,"google":false,"viber":false,"stumbleupon":false,"qzone":false,"linkedin":false},"description":"layuimini详细开发文档，最简单好用的后台模板。"},"file":{"path":"init/netcore.md","mtime":"2021-04-06T14:10:57.913Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-04-06T14:17:05.226Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-anchor-nav-x/lib/handler.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-hide-element/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-code/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-insert-logo/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-github/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-github-buttons/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-donate/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-baidu-tongji/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search/search-engine.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-lunr/lunr.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-lunr/search-lunr.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

