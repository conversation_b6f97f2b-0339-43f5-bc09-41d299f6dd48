{"name": "select-input2.x", "version": "1.0.0", "description": "Layui select配合input实现可输入，可选择，可搜索，支持异步加载，远程搜索，也可以本地data直接赋值，主要使用场景是select框可以自己输入，就是在下拉列表里找不到自己想要的选项就可以自己输入，同时还要支持模糊匹配功能，数据源可以从本地赋值，也可以异步url请求加载，或者直接远程请求联想", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=dev node_modules/.bin/webpack-dev-server --config webpack.config.js --mode development", "build": "cross-env NODE_ENV=prod webpack --config webpack.config.js --mode production"}, "author": "jerry<PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^0.24.0", "babel-polyfill": "^6.26.0", "clean-webpack-plugin": "^4.0.0", "lodash": "^4.17.21", "preact": "^10.6.4"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.4", "babel-loader": "^8.2.3", "babel-plugin-transform-react-jsx": "^6.24.1", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "less": "3.9.0", "less-loader": "4.1.0", "style-loader": "^3.3.1", "url-loader": "^4.1.1", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.6.0"}}