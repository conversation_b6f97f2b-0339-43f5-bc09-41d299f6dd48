
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>简介 · layuimini开发手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="zhongshaofa">
        
        
    
    <link rel="stylesheet" href="gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-anchor-nav-x/style/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-tbfed-pagefooter/footer.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-image-captions/image-captions.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-code/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-insert-logo/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-donate/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="gitbook/images/favicon.ico" type="image/x-icon">

    
    

    <style>
    @media only screen and (max-width: 640px) {
        .book-header .hidden-mobile {
            display: none;
        }
    }
    </style>
    <script>
        window["gitbook-plugin-github-buttons"] = {"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]};
    </script>

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter active" data-level="1.1" data-path="./">
            
                <a href="./">
            
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" >
            
                <span>
            
                    
                    初始化接口后端示例(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="init/sql.html">
            
                <a href="init/sql.html">
            
                    
                    数据库结构示例
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="init/laravel.html">
            
                <a href="init/laravel.html">
            
                    
                    PHP示例(Laravel)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="init/thinkphp.html">
            
                <a href="init/thinkphp.html">
            
                    
                    PHP示例(ThinkPHP)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.4" data-path="init/golang.html">
            
                <a href="init/golang.html">
            
                    
                    Golang示例(beego)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.5" data-path="init/java.html">
            
                <a href="init/java.html">
            
                    
                    Java示例(spring)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.6" data-path="init/netcore.html">
            
                <a href="init/netcore.html">
            
                    
                    ASP.NET CORE WebApi示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" >
            
                <span>
            
                    
                    使用说明(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.2" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.3" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.4" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.5" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.6" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（标签）">
            
                    
                    在页面中弹出新的Tab窗口（标签）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.7" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（JS方法）">
            
                    
                    在页面中弹出新的Tab窗口（JS方法）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.8" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.9" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.10" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html">
            
                    
                    单页版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.3" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.4" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.5" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.6" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在页面中打开新的页面">
            
                    
                    在页面中打开新的页面
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.7" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在内容页面中返回主页">
            
                    
                    在内容页面中返回主页
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.8" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.9" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" >
            
                <span>
            
                    
                    使用说明(V1版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="iframe.html">
            
                <a href="iframe.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1.1" data-path="iframe.html">
            
                <a href="iframe.html#默认配置说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.2" data-path="iframe.html">
            
                <a href="iframe.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.3" data-path="iframe.html">
            
                <a href="iframe.html#初始化api地址返回的参数说明">
            
                    
                    初始化api地址返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.4" data-path="iframe.html">
            
                <a href="iframe.html#在页面中弹出新的Tab窗口">
            
                    
                    在页面中弹出新的Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.5" data-path="iframe.html">
            
                <a href="iframe.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.6" data-path="iframe.html">
            
                <a href="iframe.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.7" data-path="iframe.html">
            
                <a href="iframe.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="onepage.html">
            
                <a href="onepage.html">
            
                    
                    单页版
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="." >简介</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-minus-circle"></i><ul><li><span class="title-icon "></span><a href="#layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;"><b></b>layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;</a></li><li><span class="title-icon "></span><a href="#&#x9879;&#x76EE;&#x4ECB;&#x7ECD;"><b></b>&#x9879;&#x76EE;&#x4ECB;&#x7ECD;</a></li><li><span class="title-icon "></span><a href="#&#x4E3B;&#x8981;&#x7279;&#x6027;"><b></b>&#x4E3B;&#x8981;&#x7279;&#x6027;</a></li><li><span class="title-icon "></span><a href="#&#x4EE3;&#x7801;&#x4ED3;&#x5E93;iframe-&#x591A;tab&#x7248;"><b></b>&#x4EE3;&#x7801;&#x4ED3;&#x5E93;(iframe &#x591A;tab&#x7248;)</a></li><li><span class="title-icon "></span><a href="#&#x4EE3;&#x7801;&#x4ED3;&#x5E93;onepage-&#x5355;&#x9875;&#x7248;"><b></b>&#x4EE3;&#x7801;&#x4ED3;&#x5E93;(onepage &#x5355;&#x9875;&#x7248;)</a></li><li><span class="title-icon "></span><a href="#&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;"><b></b>&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;</a></li><li><span class="title-icon "></span><a href="#&#x6548;&#x679C;&#x9884;&#x89C8;"><b></b>&#x6548;&#x679C;&#x9884;&#x89C8;</a></li><li><span class="title-icon "></span><a href="#&#x6350;&#x8D60;&#x652F;&#x6301;"><b></b>&#x6350;&#x8D60;&#x652F;&#x6301;</a></li></ul></div><a href="#layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><h1 id="layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;"><a name="layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;" class="anchor-navigation-ex-anchor" href="#layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;"><i class="fa fa-link" aria-hidden="true"></i></a>layuimini&#x540E;&#x53F0;&#x6A21;&#x677F;</h1>
<h1 id="&#x9879;&#x76EE;&#x4ECB;&#x7ECD;"><a name="&#x9879;&#x76EE;&#x4ECB;&#x7ECD;" class="anchor-navigation-ex-anchor" href="#&#x9879;&#x76EE;&#x4ECB;&#x7ECD;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x9879;&#x76EE;&#x4ECB;&#x7ECD;</h1>
<p>&#x6700;&#x7B80;&#x6D01;&#x3001;&#x6E05;&#x723D;&#x3001;&#x6613;&#x7528;&#x7684;layui&#x540E;&#x53F0;&#x6846;&#x67B6;&#x6A21;&#x677F;&#x3002;</p>
<p>&#x9879;&#x76EE;&#x4F1A;&#x4E0D;&#x5B9A;&#x65F6;&#x8FDB;&#x884C;&#x66F4;&#x65B0;&#xFF0C;&#x5EFA;&#x8BAE;star&#x548C;watch&#x4E00;&#x4EFD;&#x3002;</p>
<p>&#x6280;&#x672F;&#x4EA4;&#x6D41;QQ&#x7FA4;&#xFF1A;<a href="https://jq.qq.com/?_wv=1027&amp;k=TYKWy5Oo" target="_blank">1165301500</a>&#x3001;<a href="https://jq.qq.com/?_wv=1027&amp;k=5lyiE2Q" target="_blank">667813249&#x1F235;</a>&#x3001;<a href="https://jq.qq.com/?_wv=1027&amp;k=5JRGVfe" target="_blank">561838086&#x1F235;</a> <code>&#x52A0;&#x7FA4;&#x8BF7;&#x5907;&#x6CE8;&#x6765;&#x6E90;&#xFF1A;&#x5982;gitee&#x3001;github&#x3001;&#x5B98;&#x7F51;&#x7B49;</code>&#x3002;</p>
<h1 id="&#x4E3B;&#x8981;&#x7279;&#x6027;"><a name="&#x4E3B;&#x8981;&#x7279;&#x6027;" class="anchor-navigation-ex-anchor" href="#&#x4E3B;&#x8981;&#x7279;&#x6027;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4E3B;&#x8981;&#x7279;&#x6027;</h1>
<ul>
<li>&#x754C;&#x9762;&#x8DB3;&#x591F;&#x7B80;&#x6D01;&#x6E05;&#x723D;&#xFF0C;&#x54CD;&#x5E94;&#x5F0F;&#x4E14;&#x9002;&#x914D;&#x624B;&#x673A;&#x7AEF;&#x3002;</li>
<li>&#x4E00;&#x4E2A;&#x63A5;&#x53E3;<code>&#x51E0;&#x884C;&#x4EE3;&#x7801;&#x800C;&#x5DF2;</code>&#x76F4;&#x63A5;&#x521D;&#x59CB;&#x5316;&#x6574;&#x4E2A;&#x6846;&#x67B6;&#xFF0C;&#x65E0;&#x9700;&#x590D;&#x6742;&#x64CD;&#x4F5C;&#x3002;</li>
<li>&#x9875;&#x9762;&#x652F;&#x6301;&#x591A;&#x914D;&#x8272;&#x65B9;&#x6848;&#xFF0C;&#x53EF;&#x81EA;&#x884C;&#x9009;&#x62E9;&#x559C;&#x6B22;&#x7684;&#x914D;&#x8272;&#x3002;</li>
<li>&#x652F;&#x6301;&#x591A;tab&#xFF0C;&#x53EF;&#x4EE5;&#x6253;&#x5F00;&#x591A;&#x7A97;&#x53E3;&#x3002;</li>
<li>&#x652F;&#x6301;&#x65E0;&#x9650;&#x7EA7;&#x83DC;&#x5355;&#x548C;&#x5BF9;font-awesome&#x56FE;&#x6807;&#x5E93;&#x7684;&#x5B8C;&#x7F8E;&#x652F;&#x6301;&#x3002;</li>
<li>&#x5931;&#x6548;&#x4EE5;&#x53CA;&#x62A5;&#x9519;&#x83DC;&#x5355;&#x65E0;&#x6CD5;&#x76F4;&#x63A5;&#x6253;&#x5F00;&#xFF0C;&#x5E76;&#x7ED9;&#x51FA;&#x5F39;&#x51FA;&#x5C42;&#x63D0;&#x793A;<code>&#x5B8C;&#x7F8E;&#x7684;&#x7EBF;&#x4E0A;&#x7528;&#x6237;&#x4F53;&#x9A8C;</code>&#x3002;</li>
<li>url&#x5730;&#x5740;hash&#x5B9A;&#x4F4D;&#xFF0C;&#x53EF;&#x4EE5;&#x6E05;&#x695A;&#x770B;&#x5230;&#x5F53;&#x524D;tab&#x7684;&#x5730;&#x5740;&#x4FE1;&#x606F;&#x3002;</li>
<li>&#x5237;&#x65B0;&#x9875;&#x9762;&#x4F1A;&#x4FDD;&#x7559;&#x5F53;&#x524D;&#x7684;&#x7A97;&#x53E3;&#xFF0C;&#x5E76;&#x4E14;&#x4F1A;&#x5B9A;&#x4F4D;&#x5F53;&#x524D;&#x7A97;&#x53E3;&#x5BF9;&#x5E94;&#x5DE6;&#x4FA7;&#x83DC;&#x5355;&#x680F;&#x3002;</li>
<li>&#x652F;&#x6301;font-awesome&#x56FE;&#x6807;&#x9009;&#x62E9;&#x63D2;&#x4EF6;</li>
</ul>
<h1 id="&#x4EE3;&#x7801;&#x4ED3;&#x5E93;iframe-&#x591A;tab&#x7248;"><a name="&#x4EE3;&#x7801;&#x4ED3;&#x5E93;iframe-&#x591A;tab&#x7248;" class="anchor-navigation-ex-anchor" href="#&#x4EE3;&#x7801;&#x4ED3;&#x5E93;iframe-&#x591A;tab&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4EE3;&#x7801;&#x4ED3;&#x5E93;(iframe &#x591A;tab&#x7248;)</h1>
<h3 id="v2&#x7248;"><a name="v2&#x7248;" class="anchor-navigation-ex-anchor" href="#v2&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>v2&#x7248;</h3>
<ul>
<li>&#x5728;&#x7EBF;&#x9884;&#x89C8;&#x5730;&#x5740;&#xFF1A;<a href="http://layuimini.99php.cn/iframe/v2/index.html" target="_blank">http://layuimini.99php.cn/iframe/v2/index.html</a></li>
<li>GitHub&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/zhongshaofa/layuimini/tree/v2" target="_blank">https://github.com/zhongshaofa/layuimini/tree/v2</a></li>
<li>Gitee&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://gitee.com/zhongshaofa/layuimini/tree/v2" target="_blank">https://gitee.com/zhongshaofa/layuimini/tree/v2</a></li>
</ul>
<h3 id="v1&#x7248;"><a name="v1&#x7248;" class="anchor-navigation-ex-anchor" href="#v1&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>v1&#x7248;</h3>
<ul>
<li>&#x5728;&#x7EBF;&#x9884;&#x89C8;&#x5730;&#x5740;&#xFF1A;<a href="http://layuimini.99php.cn/iframe/v1/index.html" target="_blank">http://layuimini.99php.cn/iframe/v1/index.html</a></li>
<li>GitHub&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/zhongshaofa/layuimini/tree/master" target="_blank">https://github.com/zhongshaofa/layuimini/tree/master</a></li>
<li>Gitee&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://gitee.com/zhongshaofa/layuimini/tree/master" target="_blank">https://gitee.com/zhongshaofa/layuimini/tree/master</a></li>
</ul>
<h1 id="&#x4EE3;&#x7801;&#x4ED3;&#x5E93;onepage-&#x5355;&#x9875;&#x7248;"><a name="&#x4EE3;&#x7801;&#x4ED3;&#x5E93;onepage-&#x5355;&#x9875;&#x7248;" class="anchor-navigation-ex-anchor" href="#&#x4EE3;&#x7801;&#x4ED3;&#x5E93;onepage-&#x5355;&#x9875;&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4EE3;&#x7801;&#x4ED3;&#x5E93;(onepage &#x5355;&#x9875;&#x7248;)</h1>
<h3 id="v2&#x7248;_1"><a name="v2&#x7248;_1" class="anchor-navigation-ex-anchor" href="#v2&#x7248;_1"><i class="fa fa-link" aria-hidden="true"></i></a>v2&#x7248;</h3>
<ul>
<li>&#x5728;&#x7EBF;&#x9884;&#x89C8;&#x5730;&#x5740;&#xFF1A;<a href="http://layuimini.99php.cn/onepage/v2/index.html" target="_blank">http://layuimini.99php.cn/onepage/v2/index.html</a></li>
<li>GitHub&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/zhongshaofa/layuimini/tree/v2-onepage" target="_blank">https://github.com/zhongshaofa/layuimini/tree/v2-onepage</a></li>
<li>Gitee&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://gitee.com/zhongshaofa/layuimini/tree/v2-onepage" target="_blank">https://gitee.com/zhongshaofa/layuimini/tree/v2-onepage</a></li>
</ul>
<h3 id="v1&#x7248;_1"><a name="v1&#x7248;_1" class="anchor-navigation-ex-anchor" href="#v1&#x7248;_1"><i class="fa fa-link" aria-hidden="true"></i></a>v1&#x7248;</h3>
<ul>
<li>&#x5728;&#x7EBF;&#x9884;&#x89C8;&#x5730;&#x5740;&#xFF1A;<a href="http://layuimini.99php.cn/onepage/v1/index.html" target="_blank">http://layuimini.99php.cn/onepage/v1/index.html</a></li>
<li>GitHub&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/zhongshaofa/layuimini/tree/onepage" target="_blank">https://github.com/zhongshaofa/layuimini/tree/onepage</a></li>
<li>Gitee&#x4ED3;&#x5E93;&#x5730;&#x5740;&#xFF1A;<a href="https://gitee.com/zhongshaofa/layuimini/tree/onepage" target="_blank">https://gitee.com/zhongshaofa/layuimini/tree/onepage</a></li>
</ul>
<h1 id="&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;"><a name="&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;" class="anchor-navigation-ex-anchor" href="#&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4E0B;&#x8F7D;&#x65B9;&#x5F0F;</h1>
<h3 id="iframe-v2&#x7248;"><a name="iframe-v2&#x7248;" class="anchor-navigation-ex-anchor" href="#iframe-v2&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>iframe v2&#x7248;</h3>
<ul>
<li>GitHub&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://github.com/zhongshaofa/layuimini -b v2</code></li>
<li>Gitee&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://gitee.com/zhongshaofa/layuimini -b v2</code></li>
</ul>
<h3 id="iframe-v1&#x7248;"><a name="iframe-v1&#x7248;" class="anchor-navigation-ex-anchor" href="#iframe-v1&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>iframe v1&#x7248;</h3>
<ul>
<li>GitHub&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://github.com/zhongshaofa/layuimini -b master</code></li>
<li>Gitee&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://gitee.com/zhongshaofa/layuimini -b master</code></li>
</ul>
<h3 id="&#x5355;&#x9875;&#x7248;-v2&#x7248;"><a name="&#x5355;&#x9875;&#x7248;-v2&#x7248;" class="anchor-navigation-ex-anchor" href="#&#x5355;&#x9875;&#x7248;-v2&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5355;&#x9875;&#x7248; v2&#x7248;</h3>
<ul>
<li>GitHub&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://github.com/zhongshaofa/layuimini -b v2-onepage</code></li>
<li>Gitee&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://gitee.com/zhongshaofa/layuimini -b v2-onepage</code></li>
</ul>
<h3 id="&#x5355;&#x9875;&#x7248;-v1&#x7248;"><a name="&#x5355;&#x9875;&#x7248;-v1&#x7248;" class="anchor-navigation-ex-anchor" href="#&#x5355;&#x9875;&#x7248;-v1&#x7248;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5355;&#x9875;&#x7248; v1&#x7248;</h3>
<ul>
<li>GitHub&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://github.com/zhongshaofa/layuimini -b onepage</code></li>
<li>Gitee&#x4E0B;&#x8F7D;&#x547D;&#x4EE4;&#xFF1A;<code>git clone https://gitee.com/zhongshaofa/layuimini -b onepage</code></li>
</ul>
<h3 id="&#x53D1;&#x884C;&#x7248;&#x5730;&#x5740;"><a name="&#x53D1;&#x884C;&#x7248;&#x5730;&#x5740;" class="anchor-navigation-ex-anchor" href="#&#x53D1;&#x884C;&#x7248;&#x5730;&#x5740;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x53D1;&#x884C;&#x7248;&#x5730;&#x5740;</h3>
<ul>
<li>GitHub&#x53D1;&#x7248;&#x5730;&#x5740;&#xFF1A;<a href="https://github.com/zhongshaofa/layuimini/releases" target="_blank">https://github.com/zhongshaofa/layuimini/releases</a></li>
<li>Gitee&#x53D1;&#x7248;&#x5730;&#x5740;&#xFF1A;<a href="https://gitee.com/zhongshaofa/layuimini/releases" target="_blank">https://gitee.com/zhongshaofa/layuimini/releases</a></li>
</ul>
<h1 id="&#x6548;&#x679C;&#x9884;&#x89C8;"><a name="&#x6548;&#x679C;&#x9884;&#x89C8;" class="anchor-navigation-ex-anchor" href="#&#x6548;&#x679C;&#x9884;&#x89C8;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x6548;&#x679C;&#x9884;&#x89C8;</h1>
<blockquote>
<p>&#x603B;&#x4F53;&#x9884;&#x89C8;</p>
</blockquote>
<figure id="fig1.1.1"><img src="images/home.png" alt="Image text"><figcaption>Figure: Image text</figcaption></figure>
<h1 id="&#x6350;&#x8D60;&#x652F;&#x6301;"><a name="&#x6350;&#x8D60;&#x652F;&#x6301;" class="anchor-navigation-ex-anchor" href="#&#x6350;&#x8D60;&#x652F;&#x6301;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x6350;&#x8D60;&#x652F;&#x6301;</h1>
<p>&#x5F00;&#x6E90;&#x9879;&#x76EE;&#x4E0D;&#x6613;&#xFF0C;&#x82E5;&#x6B64;&#x9879;&#x76EE;&#x80FD;&#x5F97;&#x5230;&#x4F60;&#x7684;&#x9752;&#x7750;&#xFF0C;&#x53EF;&#x4EE5;&#x6350;&#x8D60;&#x652F;&#x6301;&#x4F5C;&#x8005;&#x6301;&#x7EED;&#x5F00;&#x53D1;&#x4E0E;&#x7EF4;&#x62A4;&#xFF0C;&#x611F;&#x8C22;&#x6240;&#x6709;&#x652F;&#x6301;&#x5F00;&#x6E90;&#x7684;&#x670B;&#x53CB;&#x3002;</p>
<p> <img src="https://chung-common.oss-cn-beijing.aliyuncs.com/donate_qrcode.png" alt="Image text"></p>
<footer class="page-footer"><span class="copyright">&#xA9; zhongshaofa all right reserved&#xFF0C;powered by Gitbook</span><span class="footer-modification">&#x6587;&#x4EF6;&#x4FEE;&#x8BA2;&#x65F6;&#x95F4;&#xFF1A;
2021-04-06 22:10:57
</span></footer>
<script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"简介","level":"1.1","depth":1,"next":{"title":"初始化接口后端示例(V2版)","level":"1.2","depth":1,"ref":"","articles":[{"title":"数据库结构示例","level":"1.2.1","depth":2,"path":"init/sql.md","ref":"init/sql.md","articles":[]},{"title":"PHP示例(Laravel)","level":"1.2.2","depth":2,"path":"init/laravel.md","ref":"init/laravel.md","articles":[]},{"title":"PHP示例(ThinkPHP)","level":"1.2.3","depth":2,"path":"init/thinkphp.md","ref":"init/thinkphp.md","articles":[]},{"title":"Golang示例(beego)","level":"1.2.4","depth":2,"path":"init/golang.md","ref":"init/golang.md","articles":[]},{"title":"Java示例(spring)","level":"1.2.5","depth":2,"path":"init/java.md","ref":"init/java.md","articles":[]},{"title":"ASP.NET CORE WebApi示例","level":"1.2.6","depth":2,"path":"init/netcore.md","ref":"init/netcore.md","articles":[]}]},"dir":"ltr"},"config":{"plugins":["anchor-nav-x","tbfed-pagefooter","search-plus","popup","highlight","image-captions","styled-blockquotes","expandable-chapters-interactive","hide-element","code","insert-logo","custom-favicon","github","github-buttons","donate","baidu-tongji"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"&copy zhongshaofa","modify_label":"文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"github":{"url":"https://github.com/zhongshaofa/layuimini"},"baidu-tongji":{"url":"https://hm.baidu.com/hm.js","token":"5637e63b6bd6247c36cf245293f121f9"},"search":{},"styled-blockquotes":{},"popup":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"donate":{"alipay":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/ali_pay.png","alipayText":"支付宝打赏","button":"打赏","title":"","wechat":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/wechat_pay.png","wechatText":"微信打赏"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"favicon":"images/favicon.ico","expandable-chapters-interactive":{},"anchor-nav-x":{"associatedWithSummary":true,"float":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showLevel":true},"github-buttons":{"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]},"custom-favicon":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"insert-logo":{"style":"background: none; max-height: 60px; min-height: 60px","url":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/logo_2.png"},"search-plus":{},"image-captions":{"variable_name":"_pictures"}},"theme":"default","author":"zhongshaofa","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[{"backlink":"index.html#fig1.1.1","level":"1.1","list_caption":"Figure: Image text","alt":"Image text","nro":1,"url":"./images/home.png","index":1,"caption_template":"Figure: _CAPTION_","label":"Image text","attributes":{},"skip":false,"key":"1.1.1"}]},"title":"layuimini开发手册","language":"zh-hans","gitbook":"*","sharing":{"qq":false,"all":["google","facebook","weibo","twitter","qq","qzone","linkedin","pocket"],"douban":false,"facebook":false,"weibo":false,"instapaper":false,"whatsapp":false,"hatenaBookmark":false,"twitter":false,"messenger":false,"line":false,"vk":false,"pocket":false,"google":false,"viber":false,"stumbleupon":false,"qzone":false,"linkedin":false},"description":"layuimini详细开发文档，最简单好用的后台模板。"},"file":{"path":"README.md","mtime":"2021-04-06T14:10:57.997Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-04-06T14:17:05.226Z"},"basePath":".","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="gitbook/gitbook.js"></script>
    <script src="gitbook/theme.js"></script>
    
        
        <script src="gitbook/gitbook-plugin-anchor-nav-x/lib/handler.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-hide-element/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-code/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-insert-logo/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github-buttons/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-donate/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-baidu-tongji/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search-engine.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/lunr.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/search-lunr.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

