﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings>
	  <add name="RFQDBContext" connectionString="Server=127.0.0.1;Database=RFQDB;User Id=sa;Password=**********;MultipleActiveResultSets=true;" providerName="System.Data.SqlClient" />
	  <add name="RFQDBEntities" connectionString="metadata=res://*/Model.Model1.csdl|res://*/Model.Model1.ssdl|res://*/Model.Model1.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=127.0.0.1;initial catalog=RFQDB;user id=sa;password=**********;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
     <!--<add name="RFQDBContext" connectionString="Server=***********;Database=HanaData;User Id=sa;Password=***********;MultipleActiveResultSets=true;" providerName="System.Data.SqlClient" />
    <add name="RFQDBEntities" connectionString="metadata=res://*/Model.Model1.csdl|res://*/Model.Model1.ssdl|res://*/Model.Model1.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=HanaData;user id=sa;password=***********;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />--> 
  </connectionStrings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
	
	
	
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>