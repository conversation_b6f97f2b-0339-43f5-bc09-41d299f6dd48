{"./": {"url": "./", "title": "简介", "keywords": "", "body": "layuimini后台模板项目介绍主要特性代码仓库(iframe 多tab版)代码仓库(onepage 单页版)下载方式效果预览捐赠支持layuimini后台模板 项目介绍 最简洁、清爽、易用的layui后台框架模板。 项目会不定时进行更新，建议star和watch一份。 技术交流QQ群：1165301500、667813249&#x1F235;、561838086&#x1F235; 加群请备注来源：如gitee、github、官网等。 主要特性 界面足够简洁清爽，响应式且适配手机端。 一个接口几行代码而已直接初始化整个框架，无需复杂操作。 页面支持多配色方案，可自行选择喜欢的配色。 支持多tab，可以打开多窗口。 支持无限级菜单和对font-awesome图标库的完美支持。 失效以及报错菜单无法直接打开，并给出弹出层提示完美的线上用户体验。 url地址hash定位，可以清楚看到当前tab的地址信息。 刷新页面会保留当前的窗口，并且会定位当前窗口对应左侧菜单栏。 支持font-awesome图标选择插件 代码仓库(iframe 多tab版) v2版 在线预览地址：http://layuimini.99php.cn/iframe/v2/index.html GitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2 Gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2 v1版 在线预览地址：http://layuimini.99php.cn/iframe/v1/index.html GitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/master Gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/master 代码仓库(onepage 单页版) v2版 在线预览地址：http://layuimini.99php.cn/onepage/v2/index.html GitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/v2-onepage Gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/v2-onepage v1版 在线预览地址：http://layuimini.99php.cn/onepage/v1/index.html GitHub仓库地址：https://github.com/zhongshaofa/layuimini/tree/onepage Gitee仓库地址：https://gitee.com/zhongshaofa/layuimini/tree/onepage 下载方式 iframe v2版 GitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b v2 Gitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b v2 iframe v1版 GitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b master Gitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b master 单页版 v2版 GitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b v2-onepage Gitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b v2-onepage 单页版 v1版 GitHub下载命令：git clone https://github.com/zhongshaofa/layuimini -b onepage Gitee下载命令：git clone https://gitee.com/zhongshaofa/layuimini -b onepage 发行版地址 GitHub发版地址：https://github.com/zhongshaofa/layuimini/releases Gitee发版地址：https://gitee.com/zhongshaofa/layuimini/releases 效果预览 总体预览 捐赠支持 开源项目不易，若此项目能得到你的青睐，可以捐赠支持作者持续开发与维护，感谢所有支持开源的朋友。 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/sql.html": {"url": "init/sql.html", "title": "数据库结构示例", "keywords": "", "body": "数据库表结构示例数据库表结构示例 后面PHP、GO的动态生成示例都是基于该表结构 CREATE TABLE `system_menu` ( `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID', `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父ID', `title` varchar(100) NOT NULL DEFAULT '' COMMENT '名称', `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '菜单图标', `href` varchar(100) NOT NULL DEFAULT '' COMMENT '链接', `target` varchar(20) NOT NULL DEFAULT '_self' COMMENT '链接打开方式', `sort` int(11) DEFAULT '0' COMMENT '菜单排序', `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)', `remark` varchar(255) DEFAULT NULL COMMENT '备注信息', `create_at` timestamp NULL DEFAULT NULL COMMENT '创建时间', `update_at` timestamp NULL DEFAULT NULL COMMENT '更新时间', `delete_at` timestamp NULL DEFAULT NULL COMMENT '删除时间', PRIMARY KEY (`id`), KEY `title` (`title`), KEY `href` (`href`) ) ENGINE=InnoDB AUTO_INCREMENT=250 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='系统菜单表'; © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/laravel.html": {"url": "init/laravel.html", "title": "PHP示例(Lara<PERSON>)", "keywords": "", "body": "Laravel框架示例(6.2版本)Laravel框架示例(6.2版本) 为了方便演示，直接用DB类去写 App/Http/Controllers/IndexController.php '首页', 'href' => 'page/welcome-1.html?t=1', ]; $logoInfo = [ 'title' => 'LAYUI MINI', 'image' => 'images/logo.png', ]; $menuInfo = $this->getMenuList(); $systemInit = [ 'homeInfo' => $homeInfo, 'logoInfo' => $logoInfo, 'menuInfo' => $menuInfo, ]; return response()->json($systemInit); } // 获取菜单列表 private function getMenuList(){ $menuList = DB::table('system_menu') ->select(['id','pid','title','icon','href','target']) ->where('status', 1) ->orderBy('sort', 'desc') ->get(); $menuList = $this->buildMenuChild(0, $menuList); return $menuList; } //递归获取子菜单 private function buildMenuChild($pid, $menuList){ $treeList = []; foreach ($menuList as $v) { if ($pid == $v->pid) { $node = (array)$v; $child = $this->buildMenuChild($v->id, $menuList); if (!empty($child)) { $node['child'] = $child; } // todo 后续此处加上用户的权限判断 $treeList[] = $node; } } return $treeList; } } © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/thinkphp.html": {"url": "init/thinkphp.html", "title": "PHP示例(ThinkPHP)", "keywords": "", "body": "ThinkPHP框架示例(6.0版本)ThinkPHP框架示例(6.0版本) 为了方便演示，直接用Db类去写 app/index/controller/Index.php '首页', 'href' => 'page/welcome-1.html?t=1', ]; $logoInfo = [ 'title' => 'LAYUI MINI', 'image' => 'images/logo.png', ]; $menuInfo = $this->getMenuList(); $systemInit = [ 'homeInfo' => $homeInfo, 'logoInfo' => $logoInfo, 'menuInfo' => $menuInfo, ]; return json($systemInit); } // 获取菜单列表 private function getMenuList(){ $menuList = Db::name('system_menu') ->field('id,pid,title,icon,href,target') ->where('status', 1) ->order('sort', 'desc') ->select(); $menuList = $this->buildMenuChild(0, $menuList); return $menuList; } //递归获取子菜单 private function buildMenuChild($pid, $menuList){ $treeList = []; foreach ($menuList as $v) { if ($pid == $v['pid']) { $node = $v; $child = $this->buildMenuChild($v['id'], $menuList); if (!empty($child)) { $node['child'] = $child; } // todo 后续此处加上用户的权限判断 $treeList[] = $node; } } return $treeList; } } © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/golang.html": {"url": "init/golang.html", "title": "Golang示例(beego)", "keywords": "", "body": "Golang动态生成初始化数据（Beego框架）Golang动态生成初始化数据（Beego框架） 对应控制器 controllers/IndexController.go package controllers import ( \"BeegoAdmin/models\" \"github.com/astaxie/beego\" ) type IndexController struct { beego.Controller } // 初始化后台框架接口 func (c *IndexController) SystemInit() { systemInit := new(models.SystemMenu).GetSystemInit() c.Data[\"json\"] = systemInit c.ServeJSON() } 对应Model models/SystemMenu.go package models import ( \"github.com/astaxie/beego/orm\" \"time\" ) // 菜单 type SystemMenu struct { Id int `json:\"id\"` Pid int `json:\"pid\"` Title string `json:\"title\"` Icon string `json:\"icon\"` Href string `json:\"href\"` Sort string `json:\"sort\"` Target string `json:\"target\"` Remark string `json:\"remark\"` Status int `json:\"status\"` CreateAt time.Time `json:\"create_at\";orm:\"auto_now;type(datetime)\"` } func (m *SystemMenu) TableName() string { return TableName(\"system_menu\") } // 初始化结构体 type SystemInit struct { HomeInfo struct { Title string `json:\"title\"` Href string `json:\"href\"` } `json:\"homeInfo\"` LogoInfo struct { Title string `json:\"title\"` Image string `json:\"image\"` } `json:\"logoInfo\"` MenuInfo []*MenuTreeList `json:\"menuInfo\"` } // 菜单结构体 type MenuTreeList struct { Id int `json:\"id\"` Pid int `json:\"pid\"` Title string `json:\"title\"` Icon string `json:\"icon\"` Href string `json:\"href\"` Target string `json:\"target\"` Remark string `json:\"remark\"` Child []*MenuTreeList `json:\"child\"` } // 获取初始化数据 func (m *SystemMenu) GetSystemInit() SystemInit { var systemInit SystemInit // 首页 systemInit.HomeInfo.Title = \"首页\" systemInit.HomeInfo.Href = \"page/welcome-1.html?t=1\" // logo systemInit.LogoInfo.Title = \"LAYUI MINI\" systemInit.LogoInfo.Image = \"images/logo.png\" // 菜单 systemInit.MenuInfo = m.GetMenuList() return systemInit } // 获取菜单列表 func (m *SystemMenu) GetMenuList() []*MenuTreeList { o := orm.NewOrm() var menuList []SystemMenu _, _ = o.QueryTable(m.TableName()).Filter(\"status\",1).OrderBy(\"-sort\").All(&menuList) return m.buildMenuChild(0, menuList) } //递归获取子菜单 func (m *SystemMenu) buildMenuChild(pid int, menuList []SystemMenu) []*MenuTreeList { var treeList []*MenuTreeList for _, v := range menuList { if pid == v.Pid { node := &MenuTreeList{ Id: v.Id, Title: v.Title, Icon: v.Icon, Href: v.Href, Target: v.Target, Pid: v.Pid, } child := v.buildMenuChild(v.Id, menuList) if len(child) != 0 { node.Child = child } // todo 后续此处加上用户的权限判断 treeList = append(treeList, node) } } return treeList } © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/java.html": {"url": "init/java.html", "title": "Java示例(spring)", "keywords": "", "body": "java动态生成初始化数据（spring框架）java动态生成初始化数据（spring框架） 示例提供来源：香喷喷的如歌 对应控制器 controllers/IndexController.java @RestController @RequestMapping(\"login\") public class LoginController { @Resource private SysMenuService sysMenuService; @GetMapping(\"/menu\") public Map menu() { return sysMenuService.menu(); } } 对应Service逻辑层 service/SysLoginServiceImpl.java @Service public class SysMenuServiceImpl implements SysMenuService { @Resource private SysMenuRepository sysMenuRepository; @Override public Map menu() { Map map = new HashMap<>(16); Map home = new HashMap<>(16); Map logo = new HashMap<>(16); List menuList = sysMenuRepository.findAllByStatusOrderBySort(true); List menuInfo = new ArrayList<>(); for (SysMenu e : menuList) { MenuVo menuVO = new MenuVo(); menuVO.setId(e.getKey().getId()); menuVO.setPid(e.getPid()); menuVO.setHref(e.getKey().getHref()); menuVO.setTitle(e.getKey().getTitle()); menuVO.setIcon(e.getIcon()); menuVO.setTarget(e.getTarget()); menuInfo.add(menuVO); } map.put(\"menuInfo\", TreeUtil.toTree(menuInfo, 0L)); home.put(\"title\",\"首页\"); home.put(\"href\",\"/page/welcome-1\");//控制器路由,自行定义 logo.put(\"title\",\"后台管理系统\"); logo.put(\"image\",\"/static/images/back.jpg\");//静态资源文件路径,可使用默认的logo.png map.put(\"homeInfo\", \"{title: '首页',href: '/ruge-web-admin/page/welcome.html'}}\"); map.put(\"logoInfo\", \"{title: 'RUGE ADMIN',image: 'images/logo.png'}\"); return map; } } TreeUtilutil/TreeUtil.java public class TreeUtil { public static List toTree(List treeList, Long pid) { List retList = new ArrayList(); for (MenuVo parent : treeList) { if (pid.equals(parent.getPid())) { retList.add(findChildren(parent, treeList)); } } return retList; } private static MenuVo findChildren(MenuVo parent, List treeList) { for (MenuVo child : treeList) { if (parent.getId().equals(child.getPid())) { if (parent.getChild() == null) { parent.setChild(new ArrayList<>()); } parent.getChild().add(findChildren(child, treeList)); } } return parent; } } repository层 reposiroty/SysMenuRepository.java public interface SysMenuRepository extends JpaRepository { //这里我只查询页面转态为启用,可自行定义和写 @Query(value = \"select * from permission_security.system_menu where STATUS = 1 ORDER BY sort \",nativeQuery = true) public List getSystemMenuByStatusAndSort(Long status,Integer sort); List findAllByStatusOrderBySort(Boolean status); } entity层 entity/MenuEntity.java @Getter @Setter @Embeddable public class MenuKey implements Serializable { private Long id; private String title; private String href; } @Getter @Setter @Entity @Table(name = \"system_menu\") public class SysMenu implements Serializable { // 复合主键要用这个注解 @EmbeddedId private MenuKey key; private Long pid; private String icon; private String target; private Integer sort; private Boolean status; private String remark; @CreatedDate private Date create_at; @CreatedDate private Date update_at; private Date delete_at; } @Data @JsonInclude(JsonInclude.Include.NON_NULL) public class MenuVo { private Long id; private Long pid; private String title; private String icon; private String href; private String target; private List child; } //这里是常规写法不使用lombok /** * insert和update注解,解决新增和更新的时候没有使用默认值 * <AUTHOR> * @Date 2020-04-25 * @Description name =\"表\" 删掉permission_security. schema=\"\"可删掉不必跟我，前提是你的表对应你的表空间就是数据库 */ @Entity @Table ( name =\"permission_security.system_menu\" , schema = \"root\") @DynamicInsert @DynamicUpdate public class SystemMenu implements Serializable { private static final long serialVersionUID = 5421757630121636006L; /**复合主键要用这个注解*/ @EmbeddedId private MenuKey key; /** * 父ID */ @Column(name = \"pid\" ) private Long pid; /** * 菜单图标 */ @Column(name = \"icon\") private String icon; /** * 链接打开方式 */ @Column(name = \"target\",columnDefinition = \"_self\") private String target; /** * 菜单排序 */ @Column(name = \"sort\" ) private Long sort; /** * 状态(0:禁用,1:启用) */ @Column(name = \"status\",columnDefinition = \"tinyint DEFAULT 1\") private Integer status; /** * 备注信息 */ @Column(name = \"remark\" ) private String remark; /** * 创建时间 */ @Column(name = \"create_at\" ) private Date createAt; /** * 更新时间 */ @Column(name = \"update_at\" ) private Date updateAt; /** * 删除时间 */ @Column(name = \"delete_at\" ) private Date deleteAt; /* public Long getId() { return this.id; } public void setId(Long id) { this.id = id; }*/ public Long getPid() { return this.pid; } public void setPid(Long pid) { this.pid = pid; } public String getIcon() { return this.icon; } public void setIcon(String icon) { this.icon = icon; } public String getTarget() { return this.target; } public void setTarget(String target) { this.target = target; } public Long getSort() { return this.sort; } public void setSort(Long sort) { this.sort = sort; } public Integer getStatus() { return this.status; } public void setStatus(Integer status) { this.status = status; } public String getRemark() { return this.remark; } public void setRemark(String remark) { this.remark = remark; } public Date getCreateAt() { return this.createAt; } public void setCreateAt(Date createAt) { this.createAt = createAt; } public Date getUpdateAt() { return this.updateAt; } public void setUpdateAt(Date updateAt) { this.updateAt = updateAt; } public Date getDeleteAt() { return this.deleteAt; } public void setDeleteAt(Date deleteAt) { this.deleteAt = deleteAt; } public MenuKey getKey() { return key; } public void setKey(MenuKey key) { this.key = key; } } @Embeddable public class MenuKey implements Serializable { @GeneratedValue(strategy = GenerationType.IDENTITY) @Column(name = \"id\") private Long id; /** * 名称 */ @Column(name = \"title\") private String title; /** * 链接 */ @Column(name = \"href\") private String href; public Long getId() { return id; } public void setId(Long id) { this.id = id; } public String getTitle() { return title; } public void setTitle(String title) { this.title = title; } public String getHref() { return href; } public void setHref(String href) { this.href = href; } } @JsonInclude(JsonInclude.Include.NON_NULL) public class MenuVo { private Long id; private Long pid; private String title; private String icon; private String href; private String target; public Long getId() { return id; } public void setId(Long id) { this.id = id; } public Long getPid() { return pid; } public void setPid(Long pid) { this.pid = pid; } public String getTitle() { return title; } public void setTitle(String title) { this.title = title; } public String getIcon() { return icon; } public void setIcon(String icon) { this.icon = icon; } public String getHref() { return href; } public void setHref(String href) { this.href = href; } public String getTarget() { return target; } public void setTarget(String target) { this.target = target; } public List getChild() { return child; } public void setChild(List child) { this.child = child; } private List child; } yml配置文件 resource/application.yml #本地开发环境配置中心 spring: application: name: springboot-webAdmin jpa: show-sql: true database: mysql #generate-ddl: true database-platform: org.hibernate.dialect.MySQL5Dialect hibernate: naming: #解决使用其他库的表时候，把小数点变成下划线，导致sql无法成功执行。 #这是由于物理命名策略引起的，大写字母变小写，加_下划线（hibernate5以上高版本） physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl #ddl-auto: update #ddl-auto: update datasource: url: ****************************************************************************************** username: root password: 123456 driver-class-name: com.mysql.jdbc.Driver type: com.zaxxer.hikari.HikariDataSource hikari: auto-commit: true minimum-idle: 2 idle-timeout: 60000 connection-timeout: 30000 max-lifetime: 1800000 pool-name: DatebookHikariCP #thymeleaf模板配置 thymeleaf: cache: false enabled: true prefix: classpath:/templates/ suffix: .html #严格执行H5标准 mode: LEGACYHTML5 encoding: UTF-8 servlet: content-type: text/html #content-type: text/html resources: chain: strategy: content: enabled: true paths: /** #静态资源路径 mvc: static-path-pattern: /static/** #关掉原生icon图标 favicon: enabled: false #项目端口 server: port: 8080 #连接超时，单位为毫秒，-1永不超时 connection-timeout: 60000 #设置tomcat参数 tomcat: uri-encoding: utf-8 max-connections: 10000 min-spare-threads: 10 #最大220个并发，可以达到不丢包（可以自己实测），默认为200。 max-threads: 220 #配置访问路径，默认为/ #servlet: #context-path: /index/main #配置日志文件参数 logging: file: path: F:/myLog/adminLog.log level: org: springframework: debug hibernate: debug © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "init/netcore.html": {"url": "init/netcore.html", "title": "ASP.NET CORE WebApi示例", "keywords": "", "body": "ASP.NET CORE WebApi接口示例ASP.NET CORE WebApi接口示例 示例提供来源：A0~海阔天空 创建ASP.NET CORE API项目数据库访问自行百度,数据库结构参考文档就行，结构都类似。 完整的后端示例地址：https://github.com/chenyi2006520/SystemMenu 数据对象如下： /// /// 菜单表 /// [Table(\"bee_system_menu\")] public class SystemMenuEntity { /// /// ID /// [Key] [Required] public long id { get; set; } /// /// 父级ID /// [Required] public long pid { get; set; } /// /// 名称 /// [Required] public string title { get; set; } /// /// 菜单图标 /// public string icon { get; set; } /// /// 链接 /// public string href { get; set; } /// /// 链接 /// public string target { get; set; } /// /// 序号 /// public int sort { get; set; } /// /// 是否菜单 /// public bool status { get; set; } } /// /// 菜单结果对象 /// public class MenusInfoResultDTO { /// /// 权限菜单树 /// public List MenuInfo { get; set; } /// /// logo /// public LogoInfo LogoInfo { get; set; } /// /// Home /// public HomeInfo HomeInfo { get; set; } } public class LogoInfo { public string title { get; set; } = \"sdsdsdsff\"; public string image { get; set; } = \"images/logo.png\"; public string href { get; set; } = \"\"; } public class HomeInfo { public string title { get; set; } = \"首页\"; public string href { get; set; } = \"page/welcome-1.html?t=1\"; } /// /// 树结构对象 /// public class SystemMenu { /// /// 数据ID /// public long Id { get; set; } /// /// 父级ID /// public long PId { get; set; } /// /// 节点名称 /// public string Title { get; set; } /// /// 节点地址 /// public string Href { get; set; } /// /// 新开Tab方式 /// public string Target { get; set; } = \"_self\"; /// /// 菜单图标样式 /// public string Icon { get; set; } /// /// 排序 /// public int Sort { get; set; } /// /// 子集 /// public List Child { get; set; } } 创建一个根对象来接受处理好的数据 SystemMenu rootNode = new SystemMenu() { Id = 0, Icon = \"\", Href = \"\", Title = \"根目录\", }; 递归处理数据库返回的数据方法参考如下， /// /// 递归处理数据 /// /// /// public static void GetTreeNodeListByNoLockedDTOArray(SystemMenuEntity[] systemMenuEntities, SystemMenu rootNode) { if (systemMenuEntities == null || systemMenuEntities.Count() p.pid == rootNode.Id); if (childreDataList != null && childreDataList.Count() > 0) { rootNode.Child = new List(); foreach (var item in childreDataList) { SystemMenu treeNode = new SystemMenu() { Id = item.id, Icon = item.icon, Href = item.href, Title = item.title, }; rootNode.Child.Add(treeNode); } foreach (var item in rootNode.Child) { GetTreeNodeListByNoLockedDTOArray(systemMenuEntities, item); } } } 最后将rootNode的Child 赋值返回给 MenusInfoResultDTO.MenuInfo 返回给前端就行 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "iframe-v2.html": {"url": "iframe-v2.html", "title": "iframe版", "keywords": "", "body": "使用说明（iframe v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中弹出新的Tab窗口（标签）在页面中弹出新的Tab窗口（JS方法）在iframe页面中关闭当前Tab窗口后台主题方案配色常见问题备注信息使用说明（iframe v2版本） 更新说明 V2版本相比于V1，核心代码进行重构，更加更加规范，配置使用起来也更方便。 js模块的拆分，代码更加规范化。 配置项移出到外部的初始化配置里面。 tab选项卡进行重构，视觉和操作体验上更加良好。 新增tab选项卡的切换与菜单之间的联动功能。 新增菜单在初始化的时候可以展开子菜单。 新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。 优化手机端初始化时的自适应，不会出现闪动的问题。 重构手机端左侧菜单，弹出菜单时不会挤压内容内面。 优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。 新增初始化加载层，更好的视觉体验 优化主题配色方案 基础参数一览表 以下参数是miniAdmin.render();初始化时进行传入。 参数 说明 类型 默认值 备注 iniUrl 初始化接口 string null 实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json clearUrl 缓存清理接口 string null 实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json urlHashLocation 是否打开hash定位 bool false 开启后，会显示路由信息，刷新页面后将定位到当前页 bgColorDefault 主题默认配置 int 0 如需添加更多主题信息，请在js/lay-module/layuimini/miniTheme.js文件内添加 multiModule 是否开启多模块 bool false 个人建议开启 menuChildOpen 是否默认展开菜单 bool false 个人建议关闭 loadingTime 初始化加载时间 0 0 建议0-2之间 pageAnim iframe窗口动画 bool false 添加tab或者切换时的过渡动漫 maxTabNum 最大的tab打开数量 int 20 防止打开太多的tab窗口导致页面卡死 示例说明 var options = { iniUrl: \"api/init.json\", // 初始化接口 clearUrl: \"api/clear.json\", // 缓存清理接口 urlHashLocation: true, // 是否打开hash定位 bgColorDefault: 0, // 主题默认配置 multiModule: true, // 是否开启多模块 menuChildOpen: false, // 是否默认展开菜单 loadingTime: 0, // 初始化加载时间 pageAnim: true, // iframe窗口动画 }; miniAdmin.render(options); 后台模板初始化 在index.html文件内进行初始化 引入lay-config.js文件，请根据实际情况修改里面扩展的路径。 引入miniAdmin模块，根据需要传入初始化参数，执行miniAdmin.render(options); 方法。 初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明 示例说明 layui.use(['jquery', 'layer', 'miniAdmin'], function () { var $ = layui.jquery, layer = layui.layer, miniAdmin = layui.miniAdmin; var options = { iniUrl: \"api/init.json\", // 初始化接口 clearUrl: \"api/clear.json\", // 缓存清理接口 urlHashLocation: true, // 是否打开hash定位 bgColorDefault: 0, // 主题默认配置 multiModule: true, // 是否开启多模块 menuChildOpen: false, // 是否默认展开菜单 }; miniAdmin.render(options); }); 初始化api接口返回的参数说明 homeInfo 是首页信息 logoInfo 是logo信息 menuInfo 是头部模块和左侧菜单对应的信息 示例说明 { \"homeInfo\": { \"title\": \"首页\", \"href\": \"page/welcome-1.html?t=1\" }, \"logoInfo\": { \"title\": \"LAYUI MINI\", \"image\": \"images/logo.png\", \"href\": \"\" }, \"menuInfo\": [ { \"title\": \"常规管理\", \"icon\": \"fa fa-address-book\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] }, { \"title\": \"组件管理\", \"icon\": \"fa fa-lemon-o\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] }, { \"title\": \"其它管理\", \"icon\": \"fa fa-slideshare\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] } ] } 缓存清理接口返回的参数说明 返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；) { \"code\": 1, \"msg\": \"清除服务端缓存成功\" } 在页面中弹出新的Tab窗口（标签） 如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入miniTab.js文件） 参数说明（layuimini-content-href：页面链接，data-title：标题） 调用方法进行监听：miniTab.listen(); 示例在page/welcome-1.html页面中有 示例说明 基本资料 layui.use(['form','miniTab'], function () { var form = layui.form, layer = layui.layer, miniTab = layui.miniTab; miniTab.listen(); }); 在页面中弹出新的Tab窗口（JS方法） 如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入miniTab.js文件） 参数说明（href：页面链接，title：标题） 示例说明 layui.use(['form','miniTab'], function () { var form = layui.form, layer = layui.layer, miniTab = layui.miniTab; // 打开新的窗口 miniTab.openNewTabByIframe({ href:\"page/form.html\", title:\"按钮示例\", }); }); 在iframe页面中关闭当前Tab窗口 如需在iframe页面中，请参考下方代码。（备注：miniTab.js文件） 调用方法：miniTab.deleteCurrentByIframe(); 示例在user-password.html,user-setting.html页面中都有 示例说明 layui.use(['form','miniTab'], function () { var form = layui.form, layer = layui.layer, miniTab = layui.miniTab; //监听提交 form.on('submit(saveBtn)', function (data) { var index = layer.alert(JSON.stringify(data.field), { title: '最终的提交信息' }, function () { layer.close(index); miniTab.deleteCurrentByIframe(); }); return false; }); }); 后台主题方案配色 系统已内置12套主题配色，如果需要自定义皮肤配色，请在miniTheme.config方法内按相同格式添加。 示例说明 var bgColorConfig = [ { headerRightBg: '#ffffff', //头部右侧背景色 headerRightBgThis: '#e4e4e4', //头部右侧选中背景色, headerRightColor: 'rgba(107, 107, 107, 0.7)', //头部右侧字体颜色, headerRightChildColor: 'rgba(107, 107, 107, 0.7)', //头部右侧下拉字体颜色, headerRightColorThis: '#565656', //头部右侧鼠标选中, headerRightNavMore: 'rgba(160, 160, 160, 0.7)', //头部右侧更多下拉颜色, headerRightNavMoreBg: '#1E9FFF', //头部右侧更多下拉列表选中背景色, headerRightNavMoreColor: '#ffffff', //头部右侧更多下拉列表字体色, headerRightToolColor: '#565656', //头部缩放按钮样式, headerLogoBg: '#192027', //logo背景颜色, headerLogoColor: 'rgb(191, 187, 187)', //logo字体颜色, leftMenuNavMore: 'rgb(191, 187, 187)', //左侧菜单更多下拉样式, leftMenuBg: '#28333E', //左侧菜单背景, leftMenuBgThis: '#1E9FFF', //左侧菜单选中背景, leftMenuChildBg: '#0c0f13', //左侧菜单子菜单背景, leftMenuColor: 'rgb(191, 187, 187)', //左侧菜单字体颜色, leftMenuColorThis: '#ffffff', //左侧菜单选中字体颜色, tabActiveColor: '#1e9fff', //tab选项卡选中颜色, }, ]; 常见问题 修改js后刷新页面未生效，请尝试清除浏览器缓存。 IIS环境下请配置支持解析.json格式文件 备注信息 菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "onepage-v2.html": {"url": "onepage-v2.html", "title": "单页版", "keywords": "", "body": "使用说明（单页面 v2版本）更新说明基础参数一览表后台模板初始化初始化api接口返回的参数说明缓存清理接口返回的参数说明在页面中打开新的页面在内容页面中返回主页后台主题方案配色常见问题备注信息使用说明（单页面 v2版本） 更新说明 V2版本相比于V1，核心代码进行重构，更加更加规范，配置使用起来也更方便。 js模块的拆分，代码更加规范化。 配置项移出到外部的初始化配置里面。 单页封装进行重构，视觉和操作体验上更加良好。 新增菜单在初始化的时候可以展开子菜单。 新增初始化时一个配置项完成多模块和单模块之间的切换，接口的初始化数据还是一样的。 优化手机端初始化时的自适应，不会出现闪动的问题。 重构手机端左侧菜单，弹出菜单时不会挤压内容内面。 优化初始化时的接口返回的数据格式api/init.json，以适配单模块的切换。 新增初始化加载层，更好的视觉体验 新增初始化页面是否加版本号 优化返回主页按钮以及页面导航栏的实现 优化主题配色方案 基础参数一览表 以下参数是miniAdmin.render();初始化时进行传入。 参数 说明 类型 默认值 备注 iniUrl 初始化接口 string null 实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json clearUrl 缓存清理接口 string null 实际使用，请对接后端接口动态生成，格式请参考文件：api/init.json renderPageVersion 初始化页面是否加版本号 bool false 开启后，页面不会有缓存问题 bgColorDefault 主题默认配置 int 0 如需添加更多主题信息，请在js/lay-module/layuimini/miniTheme.js文件内添加 multiModule 是否开启多模块 bool false 个人建议开启 menuChildOpen 是否默认展开菜单 bool false 个人建议关闭 loadingTime 初始化加载时间 0 0 建议0-2之间 示例说明 var options = { iniUrl: \"api/init.json\", // 初始化接口 clearUrl: \"api/clear.json\", // 缓存清理接口 urlHashLocation: true, // 是否打开hash定位 bgColorDefault: 0, // 主题默认配置 multiModule: true, // 是否开启多模块 menuChildOpen: false, // 是否默认展开菜单 loadingTime: 0, // 初始化加载时间 }; miniAdmin.render(options); 后台模板初始化 在index.html文件内进行初始化 引入lay-config.js文件，请根据实际情况修改里面扩展的路径。 引入miniAdmin模块，根据需要传入初始化参数，执行miniAdmin.render(options); 方法。 初始化api接口返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明 示例说明 layui.use(['jquery', 'layer', 'miniAdmin'], function () { var $ = layui.jquery, layer = layui.layer, miniAdmin = layui.miniAdmin; var options = { iniUrl: \"api/init.json\", // 初始化接口 clearUrl: \"api/clear.json\", // 缓存清理接口 urlHashLocation: true, // 是否打开hash定位 bgColorDefault: 0, // 主题默认配置 multiModule: true, // 是否开启多模块 menuChildOpen: false, // 是否默认展开菜单 }; miniAdmin.render(options); }); 初始化api接口返回的参数说明 homeInfo 是首页信息 logoInfo 是logo信息 menuInfo 是头部模块和左侧菜单对应的信息 示例说明 { \"homeInfo\": { \"title\": \"首页\", \"href\": \"page/welcome-1.html?t=1\" }, \"logoInfo\": { \"title\": \"LAYUI MINI\", \"image\": \"images/logo.png\", \"href\": \"\" }, \"menuInfo\": [ { \"title\": \"常规管理\", \"icon\": \"fa fa-address-book\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] }, { \"title\": \"组件管理\", \"icon\": \"fa fa-lemon-o\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] }, { \"title\": \"其它管理\", \"icon\": \"fa fa-slideshare\", \"href\": \"\", \"target\": \"_self\", \"child\":[...] } ] } 缓存清理接口返回的参数说明 返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；) { \"code\": 1, \"msg\": \"清除服务端缓存成功\" } 在页面中打开新的页面 在页面中打开新的页面，请参考下方代码。（备注：需要引入miniPage.js文件） 参数说明（layuimini-content-href=：页面链接，data-title：标题） 调用方法进行监听：miniPage.listen();(备注：框架初始化时已经进行监听，一般情况下不需要再次操作) 示例在page/welcome-1.html页面中有 基本资料 在内容页面中返回主页 方法一：添加class样式layuimini-back-home首页 方法二：miniPage.hashHome();，示例在user-password.html,user-setting.html页面中都有 示例说明 layui.use(['form','miniPage'], function () { var form = layui.form, layer = layui.layer, miniPage = layui.miniPage; /** * 初始化表单，要加上，不然刷新部分组件可能会不加载 */ form.render(); //监听提交 form.on('submit(saveBtn)', function (data) { var index = layer.alert(JSON.stringify(data.field), { title: '最终的提交信息' }, function () { layer.close(index); miniPage.hashHome(); }); return false; }); }); 后台主题方案配色 系统已内置12套主题配色，如果需要自定义皮肤配色，请在miniTheme.bgColorConfig方法内按相同格式添加。 示例说明 var bgColorConfig = [ { headerRight: '#1aa094', headerRightThis: '#197971', headerLogo: '#243346', menuLeft: '#2f4056', menuLeftThis: '#1aa094', menuLeftHover: '#3b3f4b', tabActive: '#1aa094', }, { headerRight: '#23262e', headerRightThis: '#0c0c0c', headerLogo: '#0c0c0c', menuLeft: '#23262e', menuLeftThis: '#737373', menuLeftHover: '#3b3f4b', tabActive: '#23262e', } ]; 常见问题 修改js后刷新页面未生效，请尝试清除浏览器缓存。 IIS环境下请配置支持解析.json格式文件 备注信息 菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "iframe.html": {"url": "iframe.html", "title": "iframe版", "keywords": "", "body": "使用说明（iframe v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中弹出新的Tab窗口在iframe页面中关闭当前Tab窗口后台主题方案配色常见问题备注信息使用说明（iframe v1版本） 默认配置说明 默认配置在layuimini.config方法内，请自行修改 urlHashLocation：是否开启URL地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页 urlSuffixDefault：是否开启URL后缀，默认开启。 BgColorDefault：系统默认皮肤，从0开始。 checkUrlDefault：是否判断URL有效，默认开启。 示例说明 var config = { urlHashLocation: true, // URL地址hash定位 urlSuffixDefault: true, // URL后缀 BgColorDefault: 0, // 默认皮肤（0开始） checkUrlDefault: true, // 是否判断URL有效 }; 后台模板初始化 在index.html文件内进行初始化 引入lay-config.js文件，请根据实际情况修改里面扩展的路径。 layuimini.init(); 方法内的参数请填写动态api地址。（实际应用中，请以后端API接口方式去实现） 初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明 示例说明 layui.use(['element', 'layer', 'layuimini'], function () { var $ = layui.jquery, element = layui.element, layer = layui.layer; layuimini.init('api/init.json'); }); 初始化api地址返回的参数说明 clearInfo是服务端清理缓存信息(clearInfo.clearUrl：服务端清理缓存接口地址，为空则不请求;) 示例说明 // 返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；) { \"code\": 1, \"msg\": \"清除服务端缓存成功\" } homeInfo 是首页信息 logoInfo 是logo信息 menuInfo 是头部模块和左侧菜单对应的信息 menuModule id必须唯一，例如 menuInfo.currency、menuInfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。 示例说明 { \"homeInfo\": { \"title\": \"首页\", \"icon\": \"fa fa-home\", \"href\": \"page/welcome-2.html?mpi=m-p-i-0\" }, \"logoInfo\": { \"title\": \"LayuiMini\", \"image\": \"images/logo.png\", \"href\": \"\" }, \"clearInfo\": { \"clearUrl\": \"api/clear.json\" }, \"menuInfo\": { \"currency\": { \"title\": \"常规管理\", \"icon\": \"fa fa-address-book\", \"child\": [ ....... ], \"other\": { \"title\": \"其它管理\", \"icon\": \"fa fa-slideshare\", \"child\": [ ....... ] } } } 在页面中弹出新的Tab窗口 如需在页面中弹出新的Tab窗口，请参考下方代码。（备注：需要引入layuimini.js文件） 参数说明（data-iframe-tab：页面链接，data-title：标题，data-icon：图标） 示例说明 基本资料 layui.config({ base: \"js/\", version: true }).extend({ layuimini: \"layuimini\" }).use(['layuimini'], function () { }); 在iframe页面中关闭当前Tab窗口 如需在iframe页面中，请参考下方代码。（备注：需要引入layuimini.js文件） 调用方法：layuimini.closeCurrentTab(); 示例在user-password.html,user-setting.html页面中都有 示例说明 layui.use(['form','layuimini'], function () { var form = layui.form, layer = layui.layer, layuimini = layui.layuimini; //监听提交 form.on('submit(saveBtn)', function (data) { var index = layer.alert(JSON.stringify(data.field), { title: '最终的提交信息' }, function () { layer.close(index); layuimini.closeCurrentTab(); }); return false; }); }); 后台主题方案配色 系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgColorConfig方法内按相同格式添加。 示例说明 var bgColorConfig = [ { headerRight: '#1aa094', headerRightThis: '#197971', headerLogo: '#243346', menuLeft: '#2f4056', menuLeftThis: '#1aa094', menuLeftHover: '#3b3f4b', }, { headerRight: '#23262e', headerRightThis: '#0c0c0c', headerLogo: '#0c0c0c', menuLeft: '#23262e', menuLeftThis: '#1aa094', menuLeftHover: '#3b3f4b', } ]; 常见问题 修改js后刷新页面未生效，请尝试清除浏览器缓存。 IIS环境下请配置支持解析.json格式文件 备注信息 菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}, "onepage.html": {"url": "onepage.html", "title": "单页版", "keywords": "", "body": "使用说明（单页面 v1版本）默认配置说明后台模板初始化初始化api地址返回的参数说明在页面中打开新页面在js中跳转页面在js中局部刷新页面后台主题方案配色常见问题备注信息使用说明（单页面 v1版本） 默认配置说明 默认配置在layuimini.config方法内，请自行修改 urlHashLocation：是否开启URL地址hash定位，默认开启。关闭后，刷新页面后将定位不到当前页，只显示主页 urlSuffixDefault：是否开启URL后缀，默认开启。 BgColorDefault：系统默认皮肤，从0开始。 示例说明 var config = { urlHashLocation: true, // URL地址hash定位 urlSuffixDefault: true, // URL后缀 BgColorDefault: 0 // 默认皮肤（0开始） }; 后台模板初始化 在index.html文件内进行初始化 引入lay-config.js文件，请根据实际情况修改里面扩展的路径。 layuimini.init(); 方法内的参数请填写动态api地址。（实际应用中，请以后端API接口方式去实现） 初始化api地址返回的参数可以参考api目录下的init.json文件或者查看使用说明的第二点的参数说明 示例说明 layui.use(['element', 'layer', 'layuimini'], function () { var $ = layui.jquery, element = layui.element, layer = layui.layer; layuimini.init('api/init.json'); }); 初始化api地址返回的参数说明 clearInfo是服务端清理缓存信息(clearInfo.clearUrl：服务端清理缓存接口地址，为空则不请求;) 示例说明 返回参数对应的事例(code：0，清除缓存失败；code：1，表示清除缓存成功；) { \"code\": 1, \"msg\": \"清除服务端缓存成功\" } homeInfo 是首页信息 logoInfo 是logo信息 menuInfo 是头部模块和左侧菜单对应的信息 menuModule id必须唯一，例如 menuInfo.currency、menuInfo.other对应的currency和other就是模块id，他们的值必须唯一，否则模块切换会有冲突。 示例说明 { \"homeInfo\": { \"title\": \"首页\", \"icon\": \"fa fa-home\", \"href\": \"page/welcome-2.html?mpi=m-p-i-0\" }, \"logoInfo\": { \"title\": \"LayuiMini\", \"image\": \"images/logo.png\", \"href\": \"\" }, \"clearInfo\": { \"clearUrl\": \"api/clear.json\" }, \"menuInfo\": { \"currency\": { \"title\": \"常规管理\", \"icon\": \"fa fa-address-book\", \"child\": [ ....... ], \"other\": { \"title\": \"其它管理\", \"icon\": \"fa fa-slideshare\", \"child\": [ ....... ] } } } 在页面中打开新页面 如需在页面中弹出新的Tab窗口，请参考下方代码。 参数说明（data-iframe-tab：页面链接，data-title：标题，data-icon：图标） 示例说明 基本资料 在js中跳转页面 如需在js跳转页面，请参考下方代码。（备注：需要引入layuimini.js文件） 调用方法：layuimini.hash(href); 示例在user-setting.html页面中 示例说明 layui.use(['form','layuimini'], function () { var form = layui.form, layer = layui.layer, layuimini = layui.layuimini; /** * 初始化表单，要加上，不然刷新部分组件可能会不加载 */ form.render(); //监听提交 form.on('submit(saveBtn)', function (data) { var index = layer.alert(JSON.stringify(data.field), { title: '最终的提交信息' }, function () { layer.close(index); layuimini.hash('page/welcome-1.html'); }); return false; }); }); 在js中局部刷新页面 如需在js局部刷新页面，请参考下方代码。（备注：需要引入layuimini.js文件） 调用方法：layuimini.refresh(); 示例在user-password.html页面中 示例说明 layui.use(['form','layuimini'], function () { var form = layui.form, layer = layui.layer, layuimini = layui.layuimini; /** * 初始化表单，要加上，不然刷新部分组件可能会不加载 */ form.render(); //监听提交 form.on('submit(saveBtn)', function (data) { var index = layer.alert(JSON.stringify(data.field), { title: '最终的提交信息' }, function () { layer.close(index); layuimini.refresh(); }); return false; }); }); 后台主题方案配色 系统已内置12套主题配色，如果需要自定义皮肤配色，请在layuimini.bgColorConfig方法内按相同格式添加。 示例说明 var bgColorConfig = [ { headerRight: '#1aa094', headerRightThis: '#197971', headerLogo: '#243346', menuLeft: '#2f4056', menuLeftThis: '#1aa094', menuLeftHover: '#3b3f4b', }, { headerRight: '#23262e', headerRightThis: '#0c0c0c', headerLogo: '#0c0c0c', menuLeft: '#23262e', menuLeftThis: '#1aa094', menuLeftHover: '#3b3f4b', } ]; 常见问题 IIS环境下请配置支持解析.json格式文件 修改js后刷新页面未生效，请尝试清除浏览器缓存。 form表单刷新，部分组件不显示的情况，请在js上加上form.render(); 备注信息 菜单栏建议最多四级菜单，四级以后菜单显示并没有那么友好。 © zhongshaofa all right reserved，powered by Gitbook文件修订时间： 2021-04-06 22:10:57 "}}