
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>单页版 · layuimini开发手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="zhongshaofa">
        
        
    
    <link rel="stylesheet" href="gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-anchor-nav-x/style/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-tbfed-pagefooter/footer.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-image-captions/image-captions.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-code/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-insert-logo/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-donate/plugin.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-search/search.css">
                
            
                
                <link rel="stylesheet" href="gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="gitbook/images/favicon.ico" type="image/x-icon">

    
    
    <link rel="prev" href="iframe.html" />
    

    <style>
    @media only screen and (max-width: 640px) {
        .book-header .hidden-mobile {
            display: none;
        }
    }
    </style>
    <script>
        window["gitbook-plugin-github-buttons"] = {"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]};
    </script>

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="./">
            
                <a href="./">
            
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" >
            
                <span>
            
                    
                    初始化接口后端示例(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="init/sql.html">
            
                <a href="init/sql.html">
            
                    
                    数据库结构示例
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="init/laravel.html">
            
                <a href="init/laravel.html">
            
                    
                    PHP示例(Laravel)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="init/thinkphp.html">
            
                <a href="init/thinkphp.html">
            
                    
                    PHP示例(ThinkPHP)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.4" data-path="init/golang.html">
            
                <a href="init/golang.html">
            
                    
                    Golang示例(beego)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.5" data-path="init/java.html">
            
                <a href="init/java.html">
            
                    
                    Java示例(spring)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.6" data-path="init/netcore.html">
            
                <a href="init/netcore.html">
            
                    
                    ASP.NET CORE WebApi示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" >
            
                <span>
            
                    
                    使用说明(V2版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1.1" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.2" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.3" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.4" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.5" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.6" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（标签）">
            
                    
                    在页面中弹出新的Tab窗口（标签）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.7" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在页面中弹出新的Tab窗口（JS方法）">
            
                    
                    在页面中弹出新的Tab窗口（JS方法）
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.8" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.9" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.1.10" data-path="iframe-v2.html">
            
                <a href="iframe-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html">
            
                    
                    单页版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#更新说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#基础参数一览表">
            
                    
                    基础参数一览表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.3" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.4" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#初始化api接口返回的参数说明">
            
                    
                    初始化api接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.5" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#缓存清理接口返回的参数说明">
            
                    
                    缓存清理接口返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.6" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在页面中打开新的页面">
            
                    
                    在页面中打开新的页面
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.7" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#在内容页面中返回主页">
            
                    
                    在内容页面中返回主页
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.8" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.9" data-path="onepage-v2.html">
            
                <a href="onepage-v2.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" >
            
                <span>
            
                    
                    使用说明(V1版)
            
                </span>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="iframe.html">
            
                <a href="iframe.html">
            
                    
                    iframe版
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1.1" data-path="iframe.html">
            
                <a href="iframe.html#默认配置说明">
            
                    
                    更新说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.2" data-path="iframe.html">
            
                <a href="iframe.html#后台模板初始化">
            
                    
                    后台模板初始化
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.3" data-path="iframe.html">
            
                <a href="iframe.html#初始化api地址返回的参数说明">
            
                    
                    初始化api地址返回的参数说明
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.4" data-path="iframe.html">
            
                <a href="iframe.html#在页面中弹出新的Tab窗口">
            
                    
                    在页面中弹出新的Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.5" data-path="iframe.html">
            
                <a href="iframe.html#在iframe页面中关闭当前Tab窗口">
            
                    
                    在iframe页面中关闭当前Tab窗口
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.1.6" data-path="iframe.html">
            
                <a href="iframe.html#后台主题方案配色">
            
                    
                    后台主题方案配色
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="*******" data-path="iframe.html">
            
                <a href="iframe.html#常见问题">
            
                    
                    常见问题
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter active" data-level="1.4.2" data-path="onepage.html">
            
                <a href="onepage.html">
            
                    
                    单页版
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="." >单页版</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-minus-circle"></i><ul><li><span class="title-icon "></span><a href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v1&#x7248;&#x672C;&#xFF09;"><b></b>&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762; v1&#x7248;&#x672C;&#xFF09;</a></li><li><span class="title-icon "></span><a href="#&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;"><b></b>&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;</a></li><li><span class="title-icon "></span><a href="#&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><b></b>&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;</a></li><li><span class="title-icon "></span><a href="#&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><b></b>&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</a></li><li><span class="title-icon "></span><a href="#&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;"><b></b>&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;</a></li><li><span class="title-icon "></span><a href="#&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;"><b></b>&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;</a></li><li><span class="title-icon "></span><a href="#&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;"><b></b>&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;</a></li><li><span class="title-icon "></span><a href="#&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><b></b>&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;</a></li><li><span class="title-icon "></span><a href="#&#x5E38;&#x89C1;&#x95EE;&#x9898;"><b></b>&#x5E38;&#x89C1;&#x95EE;&#x9898;</a></li><li><span class="title-icon "></span><a href="#&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><b></b>&#x5907;&#x6CE8;&#x4FE1;&#x606F;</a></li></ul></div><a href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v1&#x7248;&#x672C;&#xFF09;" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><h1 id="&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v1&#x7248;&#x672C;&#xFF09;"><a name="&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v1&#x7248;&#x672C;&#xFF09;" class="anchor-navigation-ex-anchor" href="#&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762;-v1&#x7248;&#x672C;&#xFF09;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#xFF08;&#x5355;&#x9875;&#x9762; v1&#x7248;&#x672C;&#xFF09;</h1>
<h1 id="&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;"><a name="&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;" class="anchor-navigation-ex-anchor" href="#&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x8BF4;&#x660E;</h1>
<ul>
<li>&#x9ED8;&#x8BA4;&#x914D;&#x7F6E;&#x5728;<code>layuimini.config</code>&#x65B9;&#x6CD5;&#x5185;&#xFF0C;&#x8BF7;&#x81EA;&#x884C;&#x4FEE;&#x6539;</li>
<li>urlHashLocation&#xFF1A;&#x662F;&#x5426;&#x5F00;&#x542F;URL&#x5730;&#x5740;hash&#x5B9A;&#x4F4D;&#xFF0C;&#x9ED8;&#x8BA4;&#x5F00;&#x542F;&#x3002;<code>&#x5173;&#x95ED;&#x540E;&#xFF0C;&#x5237;&#x65B0;&#x9875;&#x9762;&#x540E;&#x5C06;&#x5B9A;&#x4F4D;&#x4E0D;&#x5230;&#x5F53;&#x524D;&#x9875;&#xFF0C;&#x53EA;&#x663E;&#x793A;&#x4E3B;&#x9875;</code></li>
<li>urlSuffixDefault&#xFF1A;&#x662F;&#x5426;&#x5F00;&#x542F;URL&#x540E;&#x7F00;&#xFF0C;&#x9ED8;&#x8BA4;&#x5F00;&#x542F;&#x3002;</li>
<li>BgColorDefault&#xFF1A;&#x7CFB;&#x7EDF;&#x9ED8;&#x8BA4;&#x76AE;&#x80A4;&#xFF0C;&#x4ECE;0&#x5F00;&#x59CB;&#x3002;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">       <span class="hljs-keyword">var</span> config = {
             <span class="hljs-attr">urlHashLocation</span>: <span class="hljs-literal">true</span>,   <span class="hljs-comment">// URL&#x5730;&#x5740;hash&#x5B9A;&#x4F4D;</span>
             urlSuffixDefault: <span class="hljs-literal">true</span>, <span class="hljs-comment">// URL&#x540E;&#x7F00;</span>
             BgColorDefault: <span class="hljs-number">0</span>       <span class="hljs-comment">// &#x9ED8;&#x8BA4;&#x76AE;&#x80A4;&#xFF08;0&#x5F00;&#x59CB;&#xFF09;</span>
          };
</code></pre>
<h1 id="&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><a name="&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;" class="anchor-navigation-ex-anchor" href="#&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x540E;&#x53F0;&#x6A21;&#x677F;&#x521D;&#x59CB;&#x5316;</h1>
<ul>
<li><p>&#x5728;<code>index.html</code>&#x6587;&#x4EF6;&#x5185;&#x8FDB;&#x884C;&#x521D;&#x59CB;&#x5316;</p>
</li>
<li><p>&#x5F15;&#x5165;<code>lay-config.js</code>&#x6587;&#x4EF6;&#xFF0C;&#x8BF7;&#x6839;&#x636E;&#x5B9E;&#x9645;&#x60C5;&#x51B5;&#x4FEE;&#x6539;&#x91CC;&#x9762;&#x6269;&#x5C55;&#x7684;&#x8DEF;&#x5F84;&#x3002;</p>
</li>
<li><p><code>layuimini.init();</code> &#x65B9;&#x6CD5;&#x5185;&#x7684;&#x53C2;&#x6570;&#x8BF7;&#x586B;&#x5199;&#x52A8;&#x6001;api&#x5730;&#x5740;&#x3002;&#xFF08;&#x5B9E;&#x9645;&#x5E94;&#x7528;&#x4E2D;&#xFF0C;&#x8BF7;&#x4EE5;<code>&#x540E;&#x7AEF;API&#x63A5;&#x53E3;</code>&#x65B9;&#x5F0F;&#x53BB;&#x5B9E;&#x73B0;&#xFF09;</p>
</li>
<li><p>&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x53EF;&#x4EE5;&#x53C2;&#x8003;<code>api&#x76EE;&#x5F55;&#x4E0B;&#x7684;init.json&#x6587;&#x4EF6;</code>&#x6216;&#x8005;&#x67E5;&#x770B;&#x4F7F;&#x7528;&#x8BF4;&#x660E;&#x7684;&#x7B2C;&#x4E8C;&#x70B9;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</p>
</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    layui.use([<span class="hljs-string">&apos;element&apos;</span>, <span class="hljs-string">&apos;layer&apos;</span>, <span class="hljs-string">&apos;layuimini&apos;</span>], <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
        <span class="hljs-keyword">var</span> $ = layui.jquery,
            element = layui.element,
            layer = layui.layer;

        layuimini.init(<span class="hljs-string">&apos;api/init.json&apos;</span>);
    });
</code></pre>
<h1 id="&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><a name="&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;" class="anchor-navigation-ex-anchor" href="#&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x521D;&#x59CB;&#x5316;api&#x5730;&#x5740;&#x8FD4;&#x56DE;&#x7684;&#x53C2;&#x6570;&#x8BF4;&#x660E;</h1>
<ul>
<li><code>clearInfo</code>&#x662F;&#x670D;&#x52A1;&#x7AEF;&#x6E05;&#x7406;&#x7F13;&#x5B58;&#x4FE1;&#x606F;(clearInfo.clearUrl&#xFF1A;&#x670D;&#x52A1;&#x7AEF;&#x6E05;&#x7406;&#x7F13;&#x5B58;&#x63A5;&#x53E3;&#x5730;&#x5740;&#xFF0C;&#x4E3A;&#x7A7A;&#x5219;&#x4E0D;&#x8BF7;&#x6C42;;)</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-json">  &#x8FD4;&#x56DE;&#x53C2;&#x6570;&#x5BF9;&#x5E94;&#x7684;&#x4E8B;&#x4F8B;(code&#xFF1A;<span class="hljs-number">0</span>&#xFF0C;&#x6E05;&#x9664;&#x7F13;&#x5B58;&#x5931;&#x8D25;&#xFF1B;code&#xFF1A;<span class="hljs-number">1</span>&#xFF0C;&#x8868;&#x793A;&#x6E05;&#x9664;&#x7F13;&#x5B58;&#x6210;&#x529F;&#xFF1B;)

  {
    <span class="hljs-string">&quot;code&quot;</span>: <span class="hljs-number">1</span>,
    <span class="hljs-string">&quot;msg&quot;</span>: <span class="hljs-string">&quot;&#x6E05;&#x9664;&#x670D;&#x52A1;&#x7AEF;&#x7F13;&#x5B58;&#x6210;&#x529F;&quot;</span>
  }
</code></pre>
<ul>
<li><p><code>homeInfo</code> &#x662F;&#x9996;&#x9875;&#x4FE1;&#x606F;</p>
</li>
<li><p><code>logoInfo</code> &#x662F;logo&#x4FE1;&#x606F;</p>
</li>
<li><p><code>menuInfo</code> &#x662F;&#x5934;&#x90E8;&#x6A21;&#x5757;&#x548C;&#x5DE6;&#x4FA7;&#x83DC;&#x5355;&#x5BF9;&#x5E94;&#x7684;&#x4FE1;&#x606F;</p>
</li>
<li><p><code>menuModule id</code>&#x5FC5;&#x987B;&#x552F;&#x4E00;&#xFF0C;&#x4F8B;&#x5982; menuInfo.currency&#x3001;menuInfo.other&#x5BF9;&#x5E94;&#x7684;currency&#x548C;other&#x5C31;&#x662F;&#x6A21;&#x5757;id&#xFF0C;&#x4ED6;&#x4EEC;&#x7684;&#x503C;&#x5FC5;&#x987B;&#x552F;&#x4E00;&#xFF0C;&#x5426;&#x5219;&#x6A21;&#x5757;&#x5207;&#x6362;&#x4F1A;&#x6709;&#x51B2;&#x7A81;&#x3002;</p>
</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-json">{
  <span class="hljs-string">&quot;homeInfo&quot;</span>: {
    <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x9996;&#x9875;&quot;</span>,
    <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-home&quot;</span>,
    <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;page/welcome-2.html?mpi=m-p-i-0&quot;</span>
  },
  <span class="hljs-string">&quot;logoInfo&quot;</span>: {
    <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;LayuiMini&quot;</span>,
    <span class="hljs-string">&quot;image&quot;</span>: <span class="hljs-string">&quot;images/logo.png&quot;</span>,
    <span class="hljs-string">&quot;href&quot;</span>: <span class="hljs-string">&quot;&quot;</span>
  },
  <span class="hljs-string">&quot;clearInfo&quot;</span>: {
    <span class="hljs-string">&quot;clearUrl&quot;</span>: <span class="hljs-string">&quot;api/clear.json&quot;</span>
  },
  <span class="hljs-string">&quot;menuInfo&quot;</span>: {
      <span class="hljs-string">&quot;currency&quot;</span>: {
        <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x5E38;&#x89C4;&#x7BA1;&#x7406;&quot;</span>,
        <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-address-book&quot;</span>,
        <span class="hljs-string">&quot;child&quot;</span>: [
            .......
        ],
      <span class="hljs-string">&quot;other&quot;</span>: {
        <span class="hljs-string">&quot;title&quot;</span>: <span class="hljs-string">&quot;&#x5176;&#x5B83;&#x7BA1;&#x7406;&quot;</span>,
        <span class="hljs-string">&quot;icon&quot;</span>: <span class="hljs-string">&quot;fa fa-slideshare&quot;</span>,
        <span class="hljs-string">&quot;child&quot;</span>: [
            .......
        ]
    }
  }
}
</code></pre>
<h1 id="&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;"><a name="&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;" class="anchor-navigation-ex-anchor" href="#&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5728;&#x9875;&#x9762;&#x4E2D;&#x6253;&#x5F00;&#x65B0;&#x9875;&#x9762;</h1>
<ul>
<li>&#x5982;&#x9700;&#x5728;&#x9875;&#x9762;&#x4E2D;&#x5F39;&#x51FA;&#x65B0;&#x7684;Tab&#x7A97;&#x53E3;&#xFF0C;&#x8BF7;&#x53C2;&#x8003;&#x4E0B;&#x65B9;&#x4EE3;&#x7801;&#x3002;</li>
<li>&#x53C2;&#x6570;&#x8BF4;&#x660E;&#xFF08;data-iframe-tab&#xFF1A;&#x9875;&#x9762;&#x94FE;&#x63A5;&#xFF0C;data-title&#xFF1A;&#x6807;&#x9898;&#xFF0C;data-icon&#xFF1A;&#x56FE;&#x6807;&#xFF09;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-html">    <span class="hljs-tag">&lt;<span class="hljs-name">a</span> <span class="hljs-attr">href</span>=<span class="hljs-string">&quot;javascript:;&quot;</span> <span class="hljs-attr">data-content-href</span>=<span class="hljs-string">&quot;page/user-setting.html&quot;</span> <span class="hljs-attr">data-title</span>=<span class="hljs-string">&quot;&#x57FA;&#x672C;&#x8D44;&#x6599;&quot;</span>&gt;</span>&#x57FA;&#x672C;&#x8D44;&#x6599;<span class="hljs-tag">&lt;/<span class="hljs-name">a</span>&gt;</span>
</code></pre>
<h1 id="&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;"><a name="&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;" class="anchor-navigation-ex-anchor" href="#&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5728;js&#x4E2D;&#x8DF3;&#x8F6C;&#x9875;&#x9762;</h1>
<ul>
<li>&#x5982;&#x9700;&#x5728;js&#x8DF3;&#x8F6C;&#x9875;&#x9762;&#xFF0C;&#x8BF7;&#x53C2;&#x8003;&#x4E0B;&#x65B9;&#x4EE3;&#x7801;&#x3002;&#xFF08;&#x5907;&#x6CE8;&#xFF1A;&#x9700;&#x8981;&#x5F15;&#x5165;layuimini.js&#x6587;&#x4EF6;&#xFF09;</li>
<li>&#x8C03;&#x7528;&#x65B9;&#x6CD5;&#xFF1A;<code>layuimini.hash(href);</code></li>
<li>&#x793A;&#x4F8B;&#x5728;<code>user-setting.html</code>&#x9875;&#x9762;&#x4E2D;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    layui.use([<span class="hljs-string">&apos;form&apos;</span>,<span class="hljs-string">&apos;layuimini&apos;</span>], <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
        <span class="hljs-keyword">var</span> form = layui.form,
            layer = layui.layer,
            layuimini = layui.layuimini;

        <span class="hljs-comment">/**
         * &#x521D;&#x59CB;&#x5316;&#x8868;&#x5355;&#xFF0C;&#x8981;&#x52A0;&#x4E0A;&#xFF0C;&#x4E0D;&#x7136;&#x5237;&#x65B0;&#x90E8;&#x5206;&#x7EC4;&#x4EF6;&#x53EF;&#x80FD;&#x4F1A;&#x4E0D;&#x52A0;&#x8F7D;
         */</span>
        form.render();

        <span class="hljs-comment">//&#x76D1;&#x542C;&#x63D0;&#x4EA4;</span>
        form.on(<span class="hljs-string">&apos;submit(saveBtn)&apos;</span>, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">data</span>) </span>{
            <span class="hljs-keyword">var</span> index = layer.alert(<span class="hljs-built_in">JSON</span>.stringify(data.field), {
                <span class="hljs-attr">title</span>: <span class="hljs-string">&apos;&#x6700;&#x7EC8;&#x7684;&#x63D0;&#x4EA4;&#x4FE1;&#x606F;&apos;</span>
            }, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
                layer.close(index);
                layuimini.hash(<span class="hljs-string">&apos;page/welcome-1.html&apos;</span>);
            });
            <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
        });

    });
</code></pre>
<h1 id="&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;"><a name="&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;" class="anchor-navigation-ex-anchor" href="#&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5728;js&#x4E2D;&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;</h1>
<ul>
<li>&#x5982;&#x9700;&#x5728;js&#x5C40;&#x90E8;&#x5237;&#x65B0;&#x9875;&#x9762;&#xFF0C;&#x8BF7;&#x53C2;&#x8003;&#x4E0B;&#x65B9;&#x4EE3;&#x7801;&#x3002;&#xFF08;&#x5907;&#x6CE8;&#xFF1A;&#x9700;&#x8981;&#x5F15;&#x5165;layuimini.js&#x6587;&#x4EF6;&#xFF09;</li>
<li>&#x8C03;&#x7528;&#x65B9;&#x6CD5;&#xFF1A;<code>layuimini.refresh();</code></li>
<li>&#x793A;&#x4F8B;&#x5728;<code>user-password.html</code>&#x9875;&#x9762;&#x4E2D;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    layui.use([<span class="hljs-string">&apos;form&apos;</span>,<span class="hljs-string">&apos;layuimini&apos;</span>], <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
        <span class="hljs-keyword">var</span> form = layui.form,
            layer = layui.layer,
            layuimini = layui.layuimini;

        <span class="hljs-comment">/**
         * &#x521D;&#x59CB;&#x5316;&#x8868;&#x5355;&#xFF0C;&#x8981;&#x52A0;&#x4E0A;&#xFF0C;&#x4E0D;&#x7136;&#x5237;&#x65B0;&#x90E8;&#x5206;&#x7EC4;&#x4EF6;&#x53EF;&#x80FD;&#x4F1A;&#x4E0D;&#x52A0;&#x8F7D;
         */</span>
        form.render();

        <span class="hljs-comment">//&#x76D1;&#x542C;&#x63D0;&#x4EA4;</span>
        form.on(<span class="hljs-string">&apos;submit(saveBtn)&apos;</span>, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params">data</span>) </span>{
            <span class="hljs-keyword">var</span> index = layer.alert(<span class="hljs-built_in">JSON</span>.stringify(data.field), {
                <span class="hljs-attr">title</span>: <span class="hljs-string">&apos;&#x6700;&#x7EC8;&#x7684;&#x63D0;&#x4EA4;&#x4FE1;&#x606F;&apos;</span>
            }, <span class="hljs-function"><span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) </span>{
                layer.close(index);
                layuimini.refresh();
            });
            <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
        });

    });
</code></pre>
<h1 id="&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><a name="&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;" class="anchor-navigation-ex-anchor" href="#&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x540E;&#x53F0;&#x4E3B;&#x9898;&#x65B9;&#x6848;&#x914D;&#x8272;</h1>
<ul>
<li>&#x7CFB;&#x7EDF;&#x5DF2;&#x5185;&#x7F6E;12&#x5957;&#x4E3B;&#x9898;&#x914D;&#x8272;&#xFF0C;&#x5982;&#x679C;&#x9700;&#x8981;&#x81EA;&#x5B9A;&#x4E49;&#x76AE;&#x80A4;&#x914D;&#x8272;&#xFF0C;&#x8BF7;&#x5728;<code>layuimini.bgColorConfig</code>&#x65B9;&#x6CD5;&#x5185;&#x6309;&#x76F8;&#x540C;&#x683C;&#x5F0F;&#x6DFB;&#x52A0;&#x3002;</li>
</ul>
<blockquote>
<p>&#x793A;&#x4F8B;&#x8BF4;&#x660E;</p>
</blockquote>
<pre><code class="lang-js">    <span class="hljs-keyword">var</span> bgColorConfig = [
        {
            <span class="hljs-attr">headerRight</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
            <span class="hljs-attr">headerRightThis</span>: <span class="hljs-string">&apos;#197971&apos;</span>,
            <span class="hljs-attr">headerLogo</span>: <span class="hljs-string">&apos;#243346&apos;</span>,
            <span class="hljs-attr">menuLeft</span>: <span class="hljs-string">&apos;#2f4056&apos;</span>,
            <span class="hljs-attr">menuLeftThis</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
            <span class="hljs-attr">menuLeftHover</span>: <span class="hljs-string">&apos;#3b3f4b&apos;</span>,
        },
        {
            <span class="hljs-attr">headerRight</span>: <span class="hljs-string">&apos;#23262e&apos;</span>,
            <span class="hljs-attr">headerRightThis</span>: <span class="hljs-string">&apos;#0c0c0c&apos;</span>,
            <span class="hljs-attr">headerLogo</span>: <span class="hljs-string">&apos;#0c0c0c&apos;</span>,
            <span class="hljs-attr">menuLeft</span>: <span class="hljs-string">&apos;#23262e&apos;</span>,
            <span class="hljs-attr">menuLeftThis</span>: <span class="hljs-string">&apos;#1aa094&apos;</span>,
            <span class="hljs-attr">menuLeftHover</span>: <span class="hljs-string">&apos;#3b3f4b&apos;</span>,
        }
    ];
</code></pre>
<h1 id="&#x5E38;&#x89C1;&#x95EE;&#x9898;"><a name="&#x5E38;&#x89C1;&#x95EE;&#x9898;" class="anchor-navigation-ex-anchor" href="#&#x5E38;&#x89C1;&#x95EE;&#x9898;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5E38;&#x89C1;&#x95EE;&#x9898;</h1>
<ul>
<li>IIS&#x73AF;&#x5883;&#x4E0B;&#x8BF7;&#x914D;&#x7F6E;&#x652F;&#x6301;&#x89E3;&#x6790;<code>.json</code>&#x683C;&#x5F0F;&#x6587;&#x4EF6;</li>
<li><font color="red">&#x4FEE;&#x6539;js&#x540E;&#x5237;&#x65B0;&#x9875;&#x9762;&#x672A;&#x751F;&#x6548;&#xFF0C;&#x8BF7;&#x5C1D;&#x8BD5;&#x6E05;&#x9664;&#x6D4F;&#x89C8;&#x5668;&#x7F13;&#x5B58;&#x3002;</font></li>
<li>form&#x8868;&#x5355;&#x5237;&#x65B0;&#xFF0C;&#x90E8;&#x5206;&#x7EC4;&#x4EF6;&#x4E0D;&#x663E;&#x793A;&#x7684;&#x60C5;&#x51B5;&#xFF0C;&#x8BF7;&#x5728;js&#x4E0A;&#x52A0;&#x4E0A;<code>form.render();</code></li>
</ul>
<h1 id="&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><a name="&#x5907;&#x6CE8;&#x4FE1;&#x606F;" class="anchor-navigation-ex-anchor" href="#&#x5907;&#x6CE8;&#x4FE1;&#x606F;"><i class="fa fa-link" aria-hidden="true"></i></a>&#x5907;&#x6CE8;&#x4FE1;&#x606F;</h1>
<ul>
<li>&#x83DC;&#x5355;&#x680F;&#x5EFA;&#x8BAE;&#x6700;&#x591A;&#x56DB;&#x7EA7;&#x83DC;&#x5355;&#xFF0C;&#x56DB;&#x7EA7;&#x4EE5;&#x540E;&#x83DC;&#x5355;&#x663E;&#x793A;&#x5E76;&#x6CA1;&#x6709;&#x90A3;&#x4E48;&#x53CB;&#x597D;&#x3002; </li>
</ul>
<footer class="page-footer"><span class="copyright">&#xA9; zhongshaofa all right reserved&#xFF0C;powered by Gitbook</span><span class="footer-modification">&#x6587;&#x4EF6;&#x4FEE;&#x8BA2;&#x65F6;&#x95F4;&#xFF1A;
2021-04-06 22:10:57
</span></footer>
<script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="iframe.html#常见问题" class="navigation navigation-prev navigation-unique" aria-label="Previous page: 常见问题">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"单页版","level":"1.4.2","depth":2,"previous":{"title":"常见问题","level":"*******","depth":3,"anchor":"#常见问题","path":"iframe.md","ref":"iframe.md#常见问题","articles":[]},"dir":"ltr"},"config":{"plugins":["anchor-nav-x","tbfed-pagefooter","search-plus","popup","highlight","image-captions","styled-blockquotes","expandable-chapters-interactive","hide-element","code","insert-logo","custom-favicon","github","github-buttons","donate","baidu-tongji"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"&copy zhongshaofa","modify_label":"文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"github":{"url":"https://github.com/zhongshaofa/layuimini"},"baidu-tongji":{"url":"https://hm.baidu.com/hm.js","token":"5637e63b6bd6247c36cf245293f121f9"},"search":{},"styled-blockquotes":{},"popup":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"donate":{"alipay":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/ali_pay.png","alipayText":"支付宝打赏","button":"打赏","title":"","wechat":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/wechat_pay.png","wechatText":"微信打赏"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"favicon":"images/favicon.ico","expandable-chapters-interactive":{},"anchor-nav-x":{"associatedWithSummary":true,"float":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showLevel":true},"github-buttons":{"buttons":[{"user":"zhongshaofa","repo":"layuimini","type":"fork","count":true,"size":"small"},{"user":"zhongshaofa","repo":"layuimini","type":"star","count":true,"size":"small"}]},"custom-favicon":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"insert-logo":{"style":"background: none; max-height: 60px; min-height: 60px","url":"https://layuimini.oss-cn-shenzhen.aliyuncs.com/logo_2.png"},"search-plus":{},"image-captions":{"variable_name":"_pictures"}},"theme":"default","author":"zhongshaofa","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[{"backlink":"index.html#fig1.1.1","level":"1.1","list_caption":"Figure: Image text","alt":"Image text","nro":1,"url":"./images/home.png","index":1,"caption_template":"Figure: _CAPTION_","label":"Image text","attributes":{},"skip":false,"key":"1.1.1"}]},"title":"layuimini开发手册","language":"zh-hans","gitbook":"*","sharing":{"qq":false,"all":["google","facebook","weibo","twitter","qq","qzone","linkedin","pocket"],"douban":false,"facebook":false,"weibo":false,"instapaper":false,"whatsapp":false,"hatenaBookmark":false,"twitter":false,"messenger":false,"line":false,"vk":false,"pocket":false,"google":false,"viber":false,"stumbleupon":false,"qzone":false,"linkedin":false},"description":"layuimini详细开发文档，最简单好用的后台模板。"},"file":{"path":"onepage.md","mtime":"2021-04-06T14:10:57.916Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-04-06T14:17:05.226Z"},"basePath":".","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="gitbook/gitbook.js"></script>
    <script src="gitbook/theme.js"></script>
    
        
        <script src="gitbook/gitbook-plugin-anchor-nav-x/lib/handler.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-expandable-chapters-interactive/expandable-chapters.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-hide-element/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-code/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-insert-logo/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-github-buttons/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-donate/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-baidu-tongji/plugin.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search-engine.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-search/search.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/lunr.min.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-lunr/search-lunr.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

