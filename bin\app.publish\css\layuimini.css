/**
配色方案（如有需要，请自行配置）
 */
/**头部-配色*/
.layui-layout-admin .layui-header {
    background-color: #1aa094 !important;
}

.layui-header > ul > .layui-nav-item.layui-this, .layuimini-tool i:hover {
    background-color: #197971 !important;
}

.layui-header .layuimini-header-content > ul > .layui-nav-item.layui-this, .layuimini-tool i:hover {
    background-color: #197971 !important;
}

/**logo-配色*/
.layui-layout-admin .layuimini-logo {
    background-color: #243346 !important;
}

/**左侧-配色*/
.layui-side.layui-bg-black, .layui-side.layui-bg-black > .layuimini-menu-left > ul {
    background-color: #2f4056 !important;
}

.layuimini-menu-left .layui-nav .layui-nav-child a:hover:not(.layui-this) {
    background-color: #3b3f4b;
}

/**左侧菜单选中-配色*/
.layui-layout-admin .layui-nav-tree .layui-this, .layui-layout-admin .layui-nav-tree .layui-this > a, .layui-layout-admin .layui-nav-tree .layui-nav-child dd.layui-this, .layui-layout-admin .layui-nav-tree .layui-nav-child dd.layui-this a {
    background-color: #1aa094 !important;
}


/**头部样式 */
.layui-layout-admin .header {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

.layuimini-header-menu, .layui-header {
    height: 60px !important;
}

.layuimini-header-menu > .layui-nav-item {
    color: #1b1d21;
    height: 60px !important;
    line-height: 60px !important;
}

.layui-header > .layui-layout-right > .layui-nav-item {
    height: 60px !important;
    line-height: 60px !important;
}

.layui-layout-left {
    left: 295px !important;
}

.layui-nav.layui-layout-left.layuimini-header-menu.layuimini-pc-show {
    font-weight: bold;
    transition: all .2s;
}


/**logo演示（通用） */
.layui-layout-admin .layuimini-logo {
    font-weight: bold;
    color: #ffffff !important;
    height: 60px !important;
    line-height: 60px !important;
    overflow: hidden;
    line-height: 64px;
    transition: all .2s !important;
}

.layui-layout-admin .layuimini-logo img {
    display: inline-block;
    height: 40px;
    vertical-align: middle;
}

.layui-layout-admin .layuimini-logo h1 {
    display: inline-block;
    margin: 0 0 0 12px;
    color: #ffffff;
    font-weight: 600;
    font-size: 20px;
    font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
    vertical-align: middle;
}

/**缩放工具（通用） */
.layuimini-tool {
    position: absolute !important;
    top: 0;
    left: 235px;
    width: 60px;
    height: 100%;
    line-height: 60px;
    text-align: center;
    color: #ffffff !important;
    transition: all .2s;
}

/**缩放工具（缩放） */
.layuimini-tool i {
    display: block;
    color: #bbe3df;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 3px;
    text-align: center;
    margin-top: 15px;
    cursor: pointer;
}

/**tab选项卡 */
.layui-tabs-body, .layui-tabs, .layui-tab, .layui-tabs-item {
    height: 100%;
    width: 100%;
}

/**左侧菜单栏 (通用) */
.layui-side.layui-bg-black {
    transition: all .2s;
}

.layui-side.layui-bg-black > .layuimini-menu-left > ul {
    transition: all .2s;
}

.layui-side.layui-bg-black > .layuimini-menu-left > ul > .layui-nav-item:first-child {
    border-top: 1px solid #4b5461;
}

.layuimini-menu-left .layui-nav .layui-nav-item a {
    height: 40px;
    line-height: 40px;
    padding-right: 30px;
}

.layuimini-menu-left .layui-nav .layui-nav-item > a {
    padding-top: 5px;
    padding-bottom: 5px;
}

.layuimini-menu-left .layui-nav .layui-nav-child .layui-nav-child {
    background: 0 0 !important
}

.layuimini-menu-left .layui-nav .layui-nav-more {
    right: 15px;
}

.layuimini-menu-left .layui-nav .layui-nav-item a:hover {
    background-color: transparent !important;
}

.layuimini-menu-left .layui-nav {
    background-color: transparent !important;
}


/**左侧菜单栏 (正常) */
.layui-layout-body .layui-nav-itemed .layui-nav-child a, .layui-layout-body .layuimini-menu-left .layui-nav .layui-nav-child a {
    padding-left: 35px;
}

.layui-layout-body .layuimini-menu-left .layui-nav .layui-nav-child .layui-nav-child a {
    padding-left: 45px;
}

.layui-layout-body .layuimini-menu-left .layui-nav .layui-nav-child .layui-nav-child .layui-nav-child a {
    padding-left: 55px;
}

.layui-layout-body .layuimini-menu-left .layui-nav .layui-nav-child .layui-nav-child .layui-nav-child .layui-nav-child a {
    padding-left: 65px;
}

.layui-layout-body .layuimini-menu-left .layui-nav .layui-nav-itemed > .layui-nav-child {
    padding: 5px 0;
}

/**内容主体（通用） */
.layui-layout-admin .layui-body {
    overflow: hidden;
    bottom: 0px !important;
    top: 60px !important;
    transition: all .2s;
}

/**选择配色方案 */
.layuimini-color .color-title {
    padding: 10px 0 10px 20px;
    border-bottom: 1px solid #d9dada;
    margin-bottom: 8px;
}

.layuimini-color .color-content {
    padding: 10px 5px 0 5px;
}

.layuimini-color .color-content ul {
    list-style: none;
    text-align: center;
}

.layuimini-color .color-content ul li {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 80px;
    height: 50px;
    margin: 0 15px 15px 0;
    padding: 2px 2px 4px 2px;
    background-color: #f2f2f2;
    cursor: pointer;
    font-size: 12px;
    color: #666;
}

.layuimini-color .color-content li.layui-this:after, .layuimini-color .color-content li:hover:after {
    width: 100%;
    height: 100%;
    padding: 4px;
    top: -5px;
    left: -5px;
    border-color: #d8d8d8;
    opacity: 1;
}

.layuimini-color .color-content li:after {
    content: '';
    position: absolute;
    z-index: 20;
    top: 50%;
    left: 50%;
    width: 1px;
    height: 0;
    border: 1px solid #f2f2f2;
    transition: all .3s;
    -webkit-transition: all .3s;
    opacity: 0;
}


/**其它 */
.layui-tab-item {
    width: 100% !important;
    height: 100% !important;
}

.layui-nav-item.layui-this {
    background-color: #1b1d21;
}

.layui-width-height {
    width: 100%;
    height: 95%;
}

.layui-tab {
    margin: 0 0 0 0;
    z-index: 99999;
}

.text-center {
    height: 30px !important;
    line-height: 30px !important;
    text-align: center !important;
}

.layui-nav {
    padding: 0 !important;
}

.layui-nav .layui-this:after, .layui-nav-bar, .layui-nav-tree .layui-nav-itemed:after {
    width: 0 !important;
    height: 0 !important;
}

.layui-layout-admin .layui-side {
    top: 60px !important;
}

.layui-tab-card {
    box-shadow: 0px 0px 0px #888888;
    border-bottom: 0;
}


/**自定义滚动条样式 */
::-webkit-scrollbar {
    display: none
}


/*移动端遮罩层*/
.layuimini-make {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, .5);
    display: none;
}

.layuimini-mini .layui-header {
    z-index: 1001;
}

/**初始化加载层*/
.layuimini-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    z-index: 999999;
}

.layuimini-loader .layuimini-loader-inner {
    display: block;
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #1E9FFF;
    animation: spin 2s linear infinite;
}

.layuimini-loader .layuimini-loader-inner:before {
    content: "";
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #1E9FFF;
    animation: spin 3s linear infinite;
}

.layuimini-loader .layuimini-loader-inner:after {
    content: "";
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #1E9FFF;
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(1turn);
    }
}

/*系统设置*/

.layuimini-color .layui-word-aux {
    position: absolute;
    left: 60px;
    top: 12px;
    font-size: 12px;
}

.layuimini-color .layui-input-block {
    margin-left: 15px;
    min-height: 36px;
}

.layuimini-color .more-menu-list {
    width: 100%;
    margin-top: 30px;
}


.layuimini-color .more-menu-item:first-child {
    border-top: 1px solid #e8e8e8;
}

.layuimini-color .more-menu-item .layui-icon {
    font-size: 18px;
    padding-right: 10px;
}

.layuimini-color .more-menu-item {
    color: #595959;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    padding: 0 25px;
    border-bottom: 1px solid #e8e8e8;
    font-style: normal;
    display: block;
}

.layuimini-color .more-menu-item:hover {
    background-color: whitesmoke;
}

.layuimini-color .more-menu-item:after {
    color: #8c8c8c;
    right: 16px;
    content: "\e602";
    position: absolute;
    font-family: layui-icon !important;
}

/**
鼠标右键
 */
.layuimini-tab-mousedown {
    display: none;
    width: 80px;
    position: absolute;
    top: 0px !important;
    left: 0px !important;
}

.layuimini-tab-mousedown dd a {
    padding: 0 15px;
    color: #484545;
}

.layuimini-tab-make {
    position: absolute;
    top: 36px;
    bottom: 0px;
    width: 100%;
    background: rgb(255, 255, 255, 0);
    padding: 0px;
    overflow: hidden;
}

/**
菜单缩放
 */
.popup-tips .layui-layer-TipsG {
    display: none;
}

.popup-tips.layui-layer-tips .layui-layer-content {
    padding: 0;
}

.popup-tips .layui-nav-tree {
    width: 150px;
    border-radius: 10px;
}

/**左侧菜单字体间距*/
.layuimini-menu-left .layui-nav-item a span {
    letter-spacing: 1px;
}

/**头部菜单字体间距*/
.layui-layout-admin .layui-header .layuimini-header-menu.layuimini-pc-show, .layui-layout-admin .layui-header .layuimini-header-menu.layuimini-mobile-show {
    letter-spacing: 1px;
}


/**左侧菜单更多下拉样式*/
.layuimini-menu-left .layui-nav-more, .layuimini-menu-left-zoom .layui-nav-more {
    font-family: layui-icon !important;
    font-size: 12px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    width: auto !important;
    height: auto;
    line-height: normal;
    border: none;
    display: inline-block;
    margin-top: -6px !important;
}

.layuimini-menu-left .layui-nav-child .layui-nav-more {
    padding: 20px 0;
}

.layuimini-menu-left .layui-nav .layui-nav-mored, .layuimini-menu-left .layui-nav-itemed > a .layui-nav-more {
    margin-top: -9px !important;
}

.layuimini-menu-left-zoom.layui-nav .layui-nav-mored, .layuimini-menu-left-zoom.layui-nav-itemed > a .layui-nav-more {
    margin-top: -9px !important;
}

.layuimini-menu-left .layui-nav-more:before, .layuimini-menu-left-zoom .layui-nav-more:before {
    content: "\e61a";
}

.layuimini-menu-left .layui-nav-itemed > a > .layui-nav-more, .layuimini-menu-left-zoom .layui-nav-itemed > a > .layui-nav-more {
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    width: 12px;
    text-align: center;
    border-style: none;
    padding: 20px 0;
}

.layuimini-menu-left .layui-nav-itemed > a > .layui-nav-more:before, .layuimini-menu-left-zoom .layui-nav-itemed > a > .layui-nav-more:before {
    content: '\e61a';
    background-color: transparent;
    display: inline-block;
    vertical-align: middle;
}

/**修复左侧菜单字体不对齐的问题*/
.layuimini-menu-left .layui-nav-item a .fa, .layuimini-menu-left .layui-nav-item a .layui-icon {
    width: 20px;
}


/**
 PC版样式
 */
@media screen and (min-width: 1025px) {
    /**头部样式（缩放） */
    .layuimini-mini .layui-layout-left.layuimini-header-menu.layuimini-pc-show {
        left: 155px !important;
    }

    /**logo演示（缩放） */
    .layuimini-mini .layui-layout-admin .layuimini-logo {
        width: 60px !important;
    }

    .layuimini-mini .layui-layout-admin .layuimini-logo h1 {
        display: none;
    }

    /**左侧菜单栏（缩放） */
    .layuimini-mini .layuimini-menu-left {
        width: 80px !important;
    }

    .layuimini-mini .layui-side.layui-bg-black, .layuimini-mini .layuimini-menu-left > ul, .layuimini-mini .layuimini-menu-left > ul li i {
        width: 60px !important;
    }

    .layuimini-mini .layuimini-menu-left > ul li span:first-child {
        display: none;
    }

    .layuimini-mini .layuimini-menu-left > ul li span:last-child {
        float: right;
        right: 7px;
    }

    .layuimini-mini .layuimini-menu-left .layui-nav .layui-nav-item a {
        height: 40px;
        line-height: 40px;
        padding-right: 0px !important;
    }

    /**内容主体（缩放） */
    .layuimini-mini .layui-layout-admin .layui-body {
        left: 60px !important;
    }

    .layuimini-mini .layuimini-tool {
        left: 95px !important;
    }

    .layuimini-pc-show {
        display: block;
    }

    .layuimini-mobile-show {
        display: none;
    }

    /**菜单缩放*/
    .layuimini-mini .layuimini-menu-left .layui-nav-more, .layuimini-mini .layuimini-menu-left .layui-nav-child {
        display: none;
    !important;
    }
}

/**
 手机自适应样式
*/
@media screen and (max-width: 1024px) {

    /**
    todo 修复低版本IOS不能滑动问题, 但还是有问题, 低版本IOS部分情况下子页面无法自适应
     */
    .layuimini-tab .layui-tab-content .layui-tab-item {
        -webkit-overflow-scrolling: touch;
        overflow: scroll;
        width: 100%;
        height: 100%;
    }

    .layuimini-tab .layui-tab-content .layui-tab-item iframe {
        height: 100%;
        width: 100%;
    }

    .layuimini-pc-show {
        display: none;
    }

    .layuimini-mobile-show {
        display: block;
    }

    .layuimini-header-content {
        left: 0;
    }

    .layui-layout-admin .layui-body .layui-tab-item.layui-show {
        border-top: 1px solid #e2e2e2;
    }

    .layuimini-all .layui-layout-left.layuimini-header-menu {
        left: 15px !important
    }

    .layuimini-mini .layui-layout-left.layuimini-header-menu {
        left: 205px !important
    }

    .layui-layout-admin .layui-nav.layui-layout-right > li:not(.layuimini-setting) {
        width: 40px !important;
    }

    .layui-layout-admin .layui-nav.layui-layout-right > li:not(.layuimini-setting) a {
        padding: 0 15px;
    }

    .layuimini-all .layui-layout-admin .layui-body {
        left: 0px !important;
    }

    .layuimini-mini .layui-layout-admin .layuimini-menu-left, .layuimini-mini .layui-header .layuimini-logo {
        left: 0;
        transition: left .2s;
        z-index: 1001 !important;
    }

    .layuimini-all .layui-layout-admin .layuimini-menu-left, .layuimini-all .layui-header .layuimini-logo {
        left: -200px;
        transition: left .2s;
        top: 0;
        z-index: 1002;
    }

    .layuimini-mini .layui-layout-admin .layui-body {
        left: 0 !important;
        transition: left .2s;
        top: 0;
        z-index: 998;
    }

    .layuimini-mini .layuimini-make {
        display: block;
    }

    .layuimini-multi-module .layuimini-header-content .layuimini-tool {
        display: none;
    }

    .layuimini-single-module .layuimini-header-content .layuimini-tool {
        left: 15px;
    }

    .layuimini-mini .layuimini-site-mobile {
        display: none !important;
    }

    .layuimini-site-mobile {
        display: block !important;
        position: fixed;
        z-index: 100000;
        bottom: 15px;
        left: 15px;
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 2px;
        text-align: center;
        background-color: rgba(0, 0, 0, .7);
        color: #fff;
    }

    .layuimini-header-content {
        z-index: 997;
    }

    /*修复UC之类的浏览器点击无效*/
    .layuimini-make {
        cursor: pointer;
    }

    .layuimini-site-mobile {
        cursor: pointer;
    }
}

@media screen and (max-width: 550px) {

    /**头部右侧数据*/
    .layuimini-multi-module.layuimini-mini .layuimini-header-content .layui-layout-right {
        display: none;
    }
}

.layui-nav-tree .layui-nav-item > a .layui-nav-more {
    padding: 25px 0;
}

.layuimini-menu-left-zoom.layui-nav .layui-nav-more {
    padding: 20px 0;
}

.layuimini-footer {
    position: fixed;
    bottom: 0;
    height: 44px;
    line-height: 44px;
    padding: 0 15px;
    box-shadow: -1px 0 4px rgb(0 0 0 / 12%);
    background-color: #fafafa;
    width: 100%;
}