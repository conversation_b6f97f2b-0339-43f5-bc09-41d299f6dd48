.gitbook-donate {
  padding: 10px 0; margin: 20px auto; width: 90%; text-align: center;
}

#rewardButton {
  cursor: pointer;
  border: 0;
  outline: 0;
  border-radius: 100%;
  padding: 0;
  margin: 0;
  letter-spacing: normal;
  text-transform: none;
  text-indent: 0px;
  text-shadow: none;
}
#rewardButton span {
  display: inline-block;
  width: 80px;
  height: 35px;
  line-height: 35px;
  border-radius: 5px;
  color: #fff;
  font-weight: 400;
  font-style: normal;
  font-variant: normal;
  font-stretch: normal;
  font-size: 18px;
  font-family: "Microsoft Yahei";
  background: #f44336;
}
#rewardButton span:hover {
  background: #f7877f;
}
#QR {
  padding-top: 20px;
}
#QR a {
  border: 0;
}
#QR img {
  width: 180px;
  max-width: 100%;
  display: inline-block;
  margin: 0.8em 2em 0 2em;
}
#wechat:hover p {
  animation: roll 0.1s infinite linear;
  -webkit-animation: roll 0.1s infinite linear;
  -moz-animation: roll 0.1s infinite linear;
}
#alipay:hover p {
  animation: roll 0.1s infinite linear;
  -webkit-animation: roll 0.1s infinite linear;
  -moz-animation: roll 0.1s infinite linear;
}
@-moz-keyframes roll {
  from {
    -webkit-transform: rotateZ(30deg);
    -moz-transform: rotateZ(30deg);
    -ms-transform: rotateZ(30deg);
    -o-transform: rotateZ(30deg);
    transform: rotateZ(30deg);
  }
  to {
    -webkit-transform: rotateZ(-30deg);
    -moz-transform: rotateZ(-30deg);
    -ms-transform: rotateZ(-30deg);
    -o-transform: rotateZ(-30deg);
    transform: rotateZ(-30deg);
  }
}
@-webkit-keyframes roll {
  from {
    -webkit-transform: rotateZ(30deg);
    -moz-transform: rotateZ(30deg);
    -ms-transform: rotateZ(30deg);
    -o-transform: rotateZ(30deg);
    transform: rotateZ(30deg);
  }
  to {
    -webkit-transform: rotateZ(-30deg);
    -moz-transform: rotateZ(-30deg);
    -ms-transform: rotateZ(-30deg);
    -o-transform: rotateZ(-30deg);
    transform: rotateZ(-30deg);
  }
}
@-o-keyframes roll {
  from {
    -webkit-transform: rotateZ(30deg);
    -moz-transform: rotateZ(30deg);
    -ms-transform: rotateZ(30deg);
    -o-transform: rotateZ(30deg);
    transform: rotateZ(30deg);
  }
  to {
    -webkit-transform: rotateZ(-30deg);
    -moz-transform: rotateZ(-30deg);
    -ms-transform: rotateZ(-30deg);
    -o-transform: rotateZ(-30deg);
    transform: rotateZ(-30deg);
  }
}
@keyframes roll {
  from {
    -webkit-transform: rotateZ(30deg);
    -moz-transform: rotateZ(30deg);
    -ms-transform: rotateZ(30deg);
    -o-transform: rotateZ(30deg);
    transform: rotateZ(30deg);
  }
  to {
    -webkit-transform: rotateZ(-30deg);
    -moz-transform: rotateZ(-30deg);
    -ms-transform: rotateZ(-30deg);
    -o-transform: rotateZ(-30deg);
    transform: rotateZ(-30deg);
  }
}
