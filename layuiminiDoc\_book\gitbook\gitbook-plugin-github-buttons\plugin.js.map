{"version": 3, "sources": ["../src/plugin.js"], "names": [], "mappings": ";AACA,YAAY,CAAC;AACb,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE,UAAU,OAAO,EAAE;AACpC,aAAS,eAAe,CAAC,OAAO,EAAE;AAC9B,cAAM,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;KAC9C;;AAED,aAAS,YAAY,CAAC,IAQjB,EAAE;YAPH,IAAI,GADc,IAQjB,CAPD,IAAI;YACJ,IAAI,GAFc,IAQjB,CAND,IAAI;YACJ,IAAI,GAHc,IAQjB,CALD,IAAI;YACJ,IAAI,GAJc,IAQjB,CAJD,IAAI;YACJ,KAAK,GALa,IAQjB,CAHD,KAAK;YACL,MAAM,GANY,IAQjB,CAFD,MAAM;YACN,KAAK,GAPa,IAQjB,CADD,KAAK;;AAEL,YAAI,UAAU,GAAG,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC;AAChD,yOAGuD,IAAI,cAAS,IAAI,cAAS,IAAI,eAAU,KAAK,cAAS,IAAI,GAAG,UAAU,kGAG7G,KAAK,qCACJ,MAAM,+CAElB;KACT;;AAED,aAAS,gBAAgB,CAAC,KAMrB,EAAE;YALH,IAAI,GADkB,KAMrB,CALD,IAAI;YACJ,IAAI,GAFkB,KAMrB,CAJD,IAAI;YACJ,KAAK,GAHiB,KAMrB,CAHD,KAAK;YACL,MAAM,GAJgB,KAMrB,CAFD,MAAM;YACN,KAAK,GALiB,KAMrB,CADD,KAAK;;AAEL,yOAGuD,IAAI,2BAAsB,KAAK,cAAS,IAAI,kGAGlF,KAAK,qCACJ,MAAM,+CAElB;KACT;;AAED,aAAS,gBAAgB,CAAC,MAAM,EAAE;YAE1B,IAAI,GAOJ,MAAM,CAPN,IAAI;YACJ,IAAI,GAMJ,MAAM,CANN,IAAI;YACJ,IAAI,GAKJ,MAAM,CALN,IAAI;YACJ,IAAI,GAIJ,MAAM,CAJN,IAAI;YACJ,KAAK,GAGL,MAAM,CAHN,KAAK;YACL,MAAM,GAEN,MAAM,CAFN,MAAM;YACN,KAAK,GACL,MAAM,CADN,KAAK;;AAGT,YAAI,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC;AAC3B,YAAI,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,CAAA,AAAC,CAAC;AACxD,YAAI,MAAM,GAAG,MAAM,KAAK,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,CAAA,AAAC,CAAC;AACxD,YAAI,KAAK,GAAG,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;;AAEvD,YAAI,IAAI,KAAK,QAAQ,EAAE;AACnB,gBAAI,aAAa,GAAG,gBAAgB,CAAC;AACjC,oBAAI,EAAJ,IAAI;AACJ,oBAAI,EAAJ,IAAI;AACJ,qBAAK,EAAL,KAAK;AACL,sBAAM,EAAN,MAAM;AACN,qBAAK,EAAL,KAAK;aACR,CAAC,CAAC;SACN,MAAM;AACH,gBAAI,aAAa,GAAG,YAAY,CAAC;AAC7B,oBAAI,EAAJ,IAAI;AACJ,oBAAI,EAAJ,IAAI;AACJ,oBAAI,EAAJ,IAAI;AACJ,oBAAI,EAAJ,IAAI;AACJ,qBAAK,EAAL,KAAK;AACL,sBAAM,EAAN,MAAM;AACN,qBAAK,EAAL,KAAK;aACR,CAAC,CAAC;SACN;AACD,uBAAe,CAAC,aAAa,CAAC,CAAC;KAClC;;AAED,aAAS,IAAI,CAAC,MAAM,EAAE;AAClB,cAAM,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;KAC5C;;;AAGD,aAAS,eAAe,GAAG;AACvB,eAAO,MAAM,CAAC,+BAA+B,CAAC,CAAC;KAClD;;;AAGD,WAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE;AAC9C,cAAM,CAAC,+BAA+B,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;KACtE,CAAC,CAAC;;AAEH,WAAO,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY;AAC3C,YAAI,CAAC,eAAe,EAAE,CAAC,CAAC;KAC3B,CAAC,CAAC;CACN,CAAC,CAAC", "file": "plugin.js", "sourcesContent": ["// LICENSE : MIT\n\"use strict\";\nrequire(['gitbook'], function (gitbook) {\n    function addBeforeHeader(element) {\n        jQuery('.book-header > h1').before(element)\n    }\n\n    function createButton({\n        user,\n        repo,\n        type,\n        size,\n        width,\n        height,\n        count\n        }) {\n        var extraParam = type === \"watch\" ? \"&v=2\" : \"\";\n        return `<a class=\"btn pull-right hidden-mobile\" aria-label=\"github\">\n            <iframe\n                style=\"display:inline-block;vertical-align:middle;\"\n                src=\"https://ghbtns.com/github-btn.html?user=${user}&repo=${repo}&type=${type}&count=${count}&size=${size}${extraParam}\"\n                frameborder=\"0\"\n                scrolling=\"0\"\n                width=\"${width}px\"\n                height=\"${height}px\"\n            ></iframe>\n        </a>`;\n    }\n\n    function createUserButton({\n        user,\n        size,\n        width,\n        height,\n        count\n        }) {\n        return `<a class=\"btn pull-right hidden-mobile\" aria-label=\"github\">\n            <iframe\n                style=\"display:inline-block;vertical-align:middle;\"\n                src=\"https://ghbtns.com/github-btn.html?user=${user}&type=follow&count=${count}&size=${size}\"\n                frameborder=\"0\"\n                scrolling=\"0\"\n                width=\"${width}px\"\n                height=\"${height}px\"\n            ></iframe>\n        </a>`;\n    }\n\n    function insertGitHubLink(button) {\n        var {\n            user,\n            repo,\n            type,\n            size,\n            width,\n            height,\n            count\n        } = button;\n\n        var size = size || \"large\";\n        var width = width || (size === \"large\" ? \"150\" : \"100\");\n        var height = height || (size === \"large\" ? \"30\" : \"20\");\n        var count = typeof count === \"boolean\" ? count : false;\n\n        if (type === 'follow') {\n            var elementString = createUserButton({\n                user,\n                size,\n                width,\n                height,\n                count                \n            });\n        } else {\n            var elementString = createButton({\n                user,\n                repo,\n                type,\n                size,\n                width,\n                height,\n                count\n            });\n        }\n        addBeforeHeader(elementString);\n    }\n\n    function init(config) {\n        config.buttons.forEach(insertGitHubLink);\n    }\n\n    // injected by html hook\n    function getPluginConfig() {\n        return window[\"gitbook-plugin-github-buttons\"];\n    }\n\n    // make sure configuration gets injected\n    gitbook.events.bind('start', function (e, config) {\n        window[\"gitbook-plugin-github-buttons\"] = config[\"github-buttons\"];\n    });\n\n    gitbook.events.bind('page.change', function () {\n        init(getPluginConfig());\n    });\n});\n"]}