body {
    margin: 15px 15px 15px 15px;
    background: #f2f2f2;
}

.layuimini-container {
    border: 1px solid #f2f2f2;
    border-radius: 5px;
    background-color: #ffffff;
    padding-bottom: 88px; // 2025年6月30日：修复body底部显示不完整的问题
}

.layuimini-main {
    margin: 10px 10px 10px 10px;
}

/**必填红点 */
.layuimini-form > .layui-form-item > .required:after {
    content: '*';
    color: red;
    position: absolute;
    margin-left: 4px;
    font-weight: bold;
    line-height: 1.8em;
    top: 6px;
    right: 5px;
}

.layuimini-form > .layui-form-item > .layui-form-label {
    width: 120px !important;
}

.layuimini-form > .layui-form-item > .layui-input-block {
    margin-left: 150px !important;
}

.layuimini-form > .layui-form-item > .layui-input-block > tip {
    display: inline-block;
    margin-top: 10px;
    line-height: 10px;
    font-size: 10px;
    color: #a29c9c;
}

/**搜索框*/
.layuimini-container .table-search-fieldset {
    margin: 0;
    border: 1px solid #e6e6e6;
    padding: 10px 20px 5px 20px;
    color: #6b6b6b;
}

/**自定义滚动条样式 */
::-webkit-scrollbar {
    width: 15px;
    height: 15px
}

::-webkit-scrollbar-track {
    background-color: transparent;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}

::-webkit-scrollbar-thumb {
    background-color: #9c9da0;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em
}
